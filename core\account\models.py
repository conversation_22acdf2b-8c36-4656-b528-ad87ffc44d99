from django.db import models
from django.contrib.auth.models import AbstractUser
import os

def user_avatar_path(instance, filename):
    # Get the file extension
    ext = filename.split('.')[-1]
    # Generate filename as user_id.extension
    filename = f"{instance.id}.{ext}"
    # Return the complete path
    return os.path.join('avatars', filename)

class Permission(models.Model):
    parentId = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, related_name='children')
    label = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    icon = models.CharField(max_length=255, blank=True, null=True)
    # type = models.CharField(max_length=20, choices=PermissionType.choices)
    type = models.IntegerField(null=True, blank=True, default=1)
    route = models.CharField(max_length=255, blank=True, null=True)
    component = models.CharField(max_length=255, blank=True, null=True)
    order = models.IntegerField(null=True, blank=True)
    hide = models.BooleanField(default=False)
    status = models.IntegerField(null=True, blank=True, default=1)
    # status = models.CharField(max_length=20, default="ENABLE", blank=True, null=True)
    new_feature = models.BooleanField(default=False)
    frame_src = models.URLField(blank=True, null=True)

    def __str__(self):
        return self.name

class Role(models.Model):
    # id = models.CharField(primary_key=True, max_length=255)
    name = models.CharField(max_length=255)
    label = models.CharField(max_length=255)
    status = models.CharField(max_length=20, default="ENABLE")
    order = models.IntegerField()
    desc = models.TextField(blank=True, null=True)
    permission = models.ManyToManyField(Permission, related_name='roles')

    def __str__(self):
        return self.name

class User(AbstractUser):
    email = models.EmailField(unique=True)
    avatar = models.ImageField(upload_to=user_avatar_path, blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    full_name = models.CharField(max_length=255, blank=True, null=True)
    region = models.CharField(max_length=100, blank=True, null=True)
    csc = models.CharField(max_length=100, blank=True, null=True)
    title = models.CharField(max_length=255, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    about = models.TextField(blank=True, null=True)
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, related_name='users')
    # permissions = models.ManyToManyField(Permission, related_name='users', blank=True)
    is_staff = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(null=False, auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = ['email']

    def __str__(self):
        return self.username
    


# class Region(models.Model):
#     key = models.CharField(max_length=10, primary_key=True, help_text="Unique identifier for the region or CSC.")
#     name = models.CharField(max_length=255, help_text="Name of the region or CSC.")
#     parent = models.ForeignKey(
#         'self',
#         null=True,
#         blank=True,
#         related_name='children',
#         on_delete=models.CASCADE,
#         help_text="Parent region if this is a sub-region or CSC."
#     )

#     def __str__(self):
#         return self.name

#     @classmethod
#     def load_from_array(cls, data, parent=None):
#         """
#         Recursively loads regions from a nested array into the database.
#         :param data: List of dictionaries representing regions or CSCs.
#         :param parent: Parent Region instance (optional).
#         """
#         for item in data:
#             # Create or update the region/CSC
#             region, created = cls.objects.get_or_create(
#                 key=item['key'],
#                 defaults={'name': item['name'], 'parent': parent}
#             )
#             # If 'children' exist, process them recursively
#             if 'children' in item:
#                 cls.load_from_array(item['children'], parent=region)



class Region(models.Model):
    csc_code = models.CharField(max_length=40, unique=True, primary_key=True)
    name = models.CharField(max_length=100)

    def __str__(self):
        return self.name

class CSCCenter(models.Model):
    csc_code = models.CharField(max_length=40, unique=True, primary_key=True)
    name = models.CharField(max_length=100)
    region = models.ForeignKey(Region, related_name='csc_centers', on_delete=models.CASCADE)

    def __str__(self):
        return self.name

class Substation(models.Model):
    name = models.CharField(max_length=100)
    region = models.ForeignKey(Region, related_name='substations', on_delete=models.CASCADE)

    def __str__(self):
        return self.name
    
    class Meta:
        unique_together = ['name', 'region']

class Feeder(models.Model):
    VOLTAGE_CHOICES = [
        ('15KV', '15KV'),
        ('33KV', '33KV'),
    ]

    feeder_name = models.CharField(max_length=100)
    voltage_level = models.CharField(
        max_length=10, 
        choices=VOLTAGE_CHOICES,
        null=True,  # Allow NULL in database
        blank=True  # Allow blank in forms
    )
    peak_load = models.FloatField(null=True, blank=True)
    length = models.FloatField(null=True, blank=True)
    number_of_transformer = models.IntegerField(null=True, blank=True)
    substation = models.ForeignKey(Substation, related_name='feeders', on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.substation.name} - {self.feeder_name}"

class RegionData(models.Model):
    region = models.OneToOneField(Region, on_delete=models.CASCADE)
    data = models.JSONField()
    last_updated = models.DateTimeField(auto_now=True)

    def save_json_data(self, json_data):
        self.data = json_data
        self.save()

    @classmethod
    def create_from_json(cls, json_data):
        # Create or update Region
        region, _ = Region.objects.get_or_create(
            csc_code=json_data['CSC Code'],
            defaults={'name': json_data['name']}
        )

        # Create or update CSC Centers
        if 'children' in json_data and 'csc_centers' in json_data['children']:
            for csc_data in json_data['children']['csc_centers']:
                CSCCenter.objects.update_or_create(
                    csc_code=csc_data['CSC Code'],
                    defaults={
                        'name': csc_data['name'],
                        'region': region
                    }
                )

        # Create or update Substations and Feeders
        if 'children' in json_data and 'substations' in json_data['children']:
            for sub_data in json_data['children']['substations']:
                substation, _ = Substation.objects.update_or_create(
                    name=sub_data['name'],
                    region=region
                )

                # Create or update Feeders
                for feeder_data in sub_data['feeders']:
                    Feeder.objects.update_or_create(
                        feeder_name=feeder_data['feeder_name'],
                        substation=substation,
                        defaults={
                            'voltage_level': feeder_data['voltage_level'],
                            'peak_load': feeder_data.get('peak_load'),
                            'length': feeder_data.get('length'),
                            'number_of_transformer': feeder_data.get('number_of_transformer')
                        }
                    )

        # Store the original JSON
        region_data, _ = cls.objects.update_or_create(
            region=region,
            defaults={'data': json_data}
        )
        return region_data

    def __str__(self):
        return f"Data for {self.region.name}"
