"""
Concurrent URL Patterns for Transformer App
==========================================

These URL patterns provide access to concurrent-optimized views that can handle
multiple simultaneous requests without connection failures.

Features:
- Database connection pooling
- Intelligent caching
- Query optimization
- Comprehensive error handling

Endpoints:
- /test/ - Test endpoint (no auth required)
- /dashboard-statistics/ - Dashboard statistics with caching
- /basestations/ - Optimized basestations list with pagination
- /transformers/ - Optimized transformers list with pagination

Usage:
    Access via: /api/transformer/concurrent/{endpoint}/

Author: Augment Agent
Date: 2025-07-28
"""

from django.urls import path
from .concurrent_views import (
    concurrent_dashboard_statistics,
    concurrent_basestations_list,
    concurrent_transformers_list,
    concurrent_test_endpoint,
    concurrent_region_dashboard,
    concurrent_nearest_basestation,
    concurrent_export_transformer_data_excel,
    ConcurrentBasestationsFilteredAPIView
)

app_name = "concurrent_transformer"

urlpatterns = [
    # ==========================================================================
    # Test Endpoint (No Authentication Required)
    # ==========================================================================
    path('test/', concurrent_test_endpoint, name='concurrent_test'),

    # ==========================================================================
    # Dashboard and Statistics Endpoints
    # ==========================================================================
    path('dashboard-statistics/', concurrent_dashboard_statistics, name='concurrent_dashboard_statistics'),

    # ==========================================================================
    # Data List Endpoints (Paginated)
    # ==========================================================================
    path('basestations/', concurrent_basestations_list, name='concurrent_basestations'),
    path('transformers/', concurrent_transformers_list, name='concurrent_transformers'),

    # ==========================================================================
    # Additional Concurrent Endpoints
    # ==========================================================================
    path('region-dashboard/', concurrent_region_dashboard, name='concurrent_region_dashboard'),
    path('nearest-basestation/', concurrent_nearest_basestation, name='concurrent_nearest_basestation'),
    path('export-excel/', concurrent_export_transformer_data_excel, name='concurrent_export_excel'),

    # ==========================================================================
    # Concurrent Filtered API View (Production-Ready)
    # ==========================================================================
    path('filtered/', ConcurrentBasestationsFilteredAPIView.as_view(), name='concurrent_filtered_api'),
]
