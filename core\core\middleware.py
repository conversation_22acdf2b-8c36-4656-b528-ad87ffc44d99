# yourapp/middleware.py
import threading
from django.utils.deprecation import MiddlewareMixin
from rest_framework.request import Request
from rest_framework_simplejwt.authentication import JWTAuthentication
import logging

logger = logging.getLogger(__name__)
thread_local = threading.local()

class CurrentUserMiddleware(MiddlewareMixin):
    def process_request(self, request):
        # logger.debug(f"Processing request: {request.method} {request.path}")
        # logger.debug(f"Authorization header: {request.headers.get('Authorization')}")
        jwt_auth = JWTAuthentication()
        try:
            user, token = jwt_auth.authenticate(Request(request))
            logger.debug(f"Authenticated user: {user}")
            thread_local.current_user = user
        except Exception as e:
            logger.debug(f"Authentication failed: {str(e)}")
            thread_local.current_user = None

    def process_response(self, request, response):
        if hasattr(thread_local, 'current_user'):
            del thread_local.current_user
        return response