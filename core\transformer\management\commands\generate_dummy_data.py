from django.core.management.base import BaseCommand
from faker import Faker
import random
from transformer.models import (
    Basestation, TransformerData, Inspection, LvFeeder,
)

fake = Faker()

class Command(BaseCommand):
    help = "Generate dummy data for all models"

    def handle(self, *args, **options):
        # Step 1: Generate dummy basestations
        self.stdout.write("Generating dummy basestations...")
        basestations = []
        for i in range(3):
            basestation = Basestation.objects.create(
                station_code=f"rsg_{i+1}",
                region=fake.city(),
                csc=fake.company(),
                substation=fake.street_name(),
                feeder=fake.word(),
                address=fake.address().replace("\n", ", "),
                gps_location=f"{fake.latitude()}, {fake.longitude()}"
            )
            basestations.append(basestation)

        # Step 2: Generate dummy transformer data
        self.stdout.write("Generating dummy transformer data...")
        transformer_data = []
        for i in range(5):
            td = TransformerData.objects.create(
                trafo_type=random.choice([x[0] for x in TransformerData.TRANSFORMER_TYPES]),
                capacity=random.choice([x[0] for x in TransformerData.CAPACITY_CHOICES]),
                dt_number=f"DT_{i+1}",
                primary_voltage=random.choice([x[0] for x in TransformerData.PRIMARY_VOLTAGE_CHOICES]),
                colling_type=random.choice([x[0] for x in TransformerData.COOLING_TYPE_CHOICES]),
                serial_number=f"SNO_{i+1}",
                manufacturer=random.choice([x[0] for x in TransformerData.MANUFACTURER_CHOICES]),
                vector_group=random.choice([x[0] for x in TransformerData.VECTOR_GROUP_CHOICES]),
                impedance_voltage=fake.random_int(min=4, max=8),
                winding_weight=fake.random_int(min=500, max=2000),
                oil_weight=fake.random_int(min=100, max=500),
                year_of_manufacturing=fake.year(),
                date_of_installation=fake.date_between(start_date="-10y", end_date="today"),
                basestation=random.choice(basestations)
            )
            transformer_data.append(td)

        # Step 3: Generate dummy inspections
        self.stdout.write("Generating dummy inspections...")
        inspections = []
        for i in range(7):
            inspection = Inspection.objects.create(
                owner=fake.name(),
                service_type=random.choice([x[0] for x in Inspection.SERVICE_TYPE_CHOICES]),
                body_condition=random.choice([x[0] for x in Inspection.CONDITION_CHOICES]),
                arrester=random.choice([x[0] for x in Inspection.STATUS_CHOICES]),
                drop_out=random.choice([x[0] for x in Inspection.STATUS_CHOICES]),
                fuse_link=random.choice([x[0] for x in Inspection.STATUS_CHOICES]),
                mv_bushing=random.choice([x[0] for x in Inspection.STATUS_CHOICES]),
                mv_cable_lug=random.choice([x[0] for x in Inspection.STATUS_CHOICES]),
                lv_bushing=random.choice([x[0] for x in Inspection.STATUS_CHOICES]),
                lv_cable_lug=random.choice([x[0] for x in Inspection.STATUS_CHOICES]),
                oil_level=random.choice([x[0] for x in Inspection.OIL_LEVEL_CHOICES]),
                insulation_level=random.choice([x[0] for x in Inspection.INSULATION_LEVEL_CHOICES]),
                horn_gap=random.choice([x[0] for x in Inspection.HORN_GAP_CHOICES]),
                silica_gel=random.choice([x[0] for x in Inspection.CONDITION_CHOICES]),
                has_linkage=random.choice([x[0] for x in Inspection.YES_NO_CHOICES]),
                arrester_body_ground=random.choice([x[0] for x in Inspection.AVAILABLE_CHOICES]),
                neutral_ground=random.choice([x[0] for x in Inspection.AVAILABLE_CHOICES]),
                status_of_mounting=random.choice([x[0] for x in Inspection.CONDITION_CHOICES]),
                mounting_condition=random.choice([x[0] for x in Inspection.CONDITION_CHOICES]),
                N_load_current=fake.random_int(min=0, max=100),
                R_S_Voltage=fake.random_int(min=200, max=400),
                R_T_Voltage=fake.random_int(min=200, max=400),
                T_S_Voltage=fake.random_int(min=200, max=400),
                transformer_data=random.choice(transformer_data)
            )
            inspections.append(inspection)

        # Step 4: Generate dummy LV feeder data
        self.stdout.write("Generating dummy LV feeder data...")
        lv_feeders = []
        for i in range(10):  # Generate 10 LV feeders
            lv_feeder = LvFeeder.objects.create(
                distribution_box_name=fake.word(ext_word_list=["Box 1", "Box 2", "Box 3",  "Box 4", "Box 5"]),
                R_load_current=fake.random_int(min=0, max=100),
                S_load_current=fake.random_int(min=0, max=100),
                T_load_current=fake.random_int(min=0, max=100),
                R_fuse_rating=fake.random_int(min=10, max=50),  # Remove 'A' suffix
                S_fuse_rating=fake.random_int(min=10, max=50),  # Remove 'A' suffix
                T_fuse_rating=fake.random_int(min=10, max=50),  # Remove 'A' suffix
                inspection_data=random.choice(inspections)  # Link to a random inspection
            )
            lv_feeders.append(lv_feeder)

        self.stdout.write(self.style.SUCCESS("Dummy data generated successfully!"))
