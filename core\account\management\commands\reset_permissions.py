from django.core.management.base import BaseCommand
from account.models import Permission

class Command(BaseCommand):
    help = "Delete all permissions and reset the data from load_permissions command"

    def handle(self, *args, **kwargs):
        # Delete all existing permissions
        self.stdout.write("Deleting all existing permissions...")
        Permission.objects.all().delete()

        # Reset the database sequence for the ID
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM sqlite_sequence WHERE name='account_permission';")
        
        self.stdout.write(self.style.SUCCESS("All permissions deleted. You can now run load_permissions to start IDs from 1"))