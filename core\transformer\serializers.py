from rest_framework import serializers
from .models import *
# from account.serializers import UserSerializer
from account.models import User
from django.core.exceptions import ValidationError


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email']

class BasestationSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)
    
    class Meta:
        model = Basestation
        fields = '__all__'  # This will automatically include the new station_type field

        extra_kwargs = {
            'updated_by': {'required': False}
        }

class TransformerDataSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)
    
    class Meta:
        model = TransformerData
        fields = '__all__'
        extra_kwargs = {
            'updated_by': {'required': False}
        }

    def create(self, validated_data):
        try:
            return super().create(validated_data)
        except ValidationError as e:
            raise serializers.ValidationError(e.message_dict)

    def update(self, instance, validated_data):
        try:
            return super().update(instance, validated_data)
        except ValidationError as e:
            raise serializers.ValidationError(e.message_dict)

# TransformerData  populated with basestation,updated_by, created_by
class TransformerDataSerializer2(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)
    basestation = BasestationSerializer(read_only=True)

    class Meta:
        model = TransformerData
        fields = '__all__'

        extra_kwargs = {
            'updated_by': {'required': False}
        }


class InspectionSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)
    
    class Meta:
        model = Inspection
        fields = '__all__'  # service_type will be automatically excluded since it's removed

class InspectionSerializer2(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)
    transformer_data = TransformerDataSerializer2(read_only=True)

    class Meta:
        model = Inspection
        fields = '__all__'


class LvFeederSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)
    
    class Meta:
        model = LvFeeder
        fields = '__all__'  # This will include the new transfromer_load field

class LvFeederSerializer2(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)
    inspection_data = InspectionSerializer(read_only=True)

    class Meta:
        model = LvFeeder
        fields = '__all__'

    

