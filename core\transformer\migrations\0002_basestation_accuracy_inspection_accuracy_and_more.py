# Generated by Django 5.1.6 on 2025-07-22 10:25

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('transformer', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='basestation',
            name='accuracy',
            field=models.CharField(blank=True, max_length=125, null=True),
        ),
        migrations.AddField(
            model_name='inspection',
            name='accuracy',
            field=models.CharField(blank=True, max_length=125, null=True),
        ),
        migrations.AddField(
            model_name='inspection',
            name='gps_location',
            field=models.CharField(blank=True, max_length=125, null=True),
        ),
        migrations.AlterField(
            model_name='transformerdata',
            name='manufacturer',
            field=models.CharField(default=django.utils.timezone.now, max_length=100, verbose_name='Manufacturer'),
            preserve_default=False,
        ),
    ]
