from django.contrib import admin
from .models import Maintenance
from logs.utils import log_activity, log_error, log_data_change

@admin.register(Maintenance)
class MaintenanceAdmin(admin.ModelAdmin):
    list_display = ('code', 'asset', 'status', 'priority', 'type', 'date_due', 'created_by')
    list_filter = ('status', 'priority', 'type')
    search_fields = ('code', 'asset', 'details')
    date_hierarchy = 'date_created'

    def save_model(self, request, obj, form, change):
        try:
            if change:
                # Get the original object from the database
                original = self.model.objects.get(pk=obj.pk)
                changed_fields = {}
                
                # Compare fields and log changes
                for field in form.changed_data:
                    old_value = getattr(original, field)
                    new_value = getattr(obj, field)
                    changed_fields[field] = new_value
                    
                    log_data_change(
                        model_instance=obj,
                        field_name=field,
                        old_value=old_value,
                        new_value=new_value,
                        user=request.user,
                        ip_address=request.META.get('REMOTE_ADDR')
                    )

                log_activity(
                    user=request.user,
                    action='UPDATE',
                    model_name='Maintenance',
                    record_id=obj.pk,
                    changes=changed_fields,
                    ip_address=request.META.get('REMOTE_ADDR')
                )
            else:
                log_activity(
                    user=request.user,
                    action='CREATE',
                    model_name='Maintenance',
                    record_id=obj.pk,
                    changes=form.cleaned_data,
                    ip_address=request.META.get('REMOTE_ADDR')
                )

            super().save_model(request, obj, form, change)
        except Exception as e:
            log_error(
                level='ERROR',
                message=f"Error saving maintenance record in admin: {str(e)}",
                user=request.user,
                traceback=str(e),
                ip_address=request.META.get('REMOTE_ADDR')
            )
            raise
