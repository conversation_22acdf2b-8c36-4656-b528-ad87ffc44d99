# Generated by Django 5.1.6 on 2025-07-23 20:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('transformer', '0002_basestation_accuracy_inspection_accuracy_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='lvfeeder',
            name='R_fuse_rating',
            field=models.CharField(choices=[('63', '63 A'), ('80', '80  A'), ('100', '100 A'), ('150', '150 A'), ('160', '160 A'), ('200', '200 A'), ('250', '250 A'), ('300', '300 A'), ('315', '315 A'), ('350', '350 A'), ('400', '400 A'), ('500', '500 A'), ('600', '600 A'), ('700', '700 A'), ('800', '800 A'), ('Direct', 'Direct'), ('other', 'Other')], max_length=10, verbose_name='<PERSON> <PERSON><PERSON>'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='lvfeeder',
            name='S_fuse_rating',
            field=models.CharField(choices=[('63', '63 A'), ('80', '80  A'), ('100', '100 A'), ('150', '150 A'), ('160', '160 A'), ('200', '200 A'), ('250', '250 A'), ('300', '300 A'), ('315', '315 A'), ('350', '350 A'), ('400', '400 A'), ('500', '500 A'), ('600', '600 A'), ('700', '700 A'), ('800', '800 A'), ('Direct', 'Direct'), ('other', 'Other')], max_length=10, verbose_name='S Fuse Rating'),
        ),
        migrations.AlterField(
            model_name='lvfeeder',
            name='T_fuse_rating',
            field=models.CharField(choices=[('63', '63 A'), ('80', '80  A'), ('100', '100 A'), ('150', '150 A'), ('160', '160 A'), ('200', '200 A'), ('250', '250 A'), ('300', '300 A'), ('315', '315 A'), ('350', '350 A'), ('400', '400 A'), ('500', '500 A'), ('600', '600 A'), ('700', '700 A'), ('800', '800 A'), ('Direct', 'Direct'), ('other', 'Other')], max_length=10, verbose_name='T Fuse Rating'),
        ),
    ]
