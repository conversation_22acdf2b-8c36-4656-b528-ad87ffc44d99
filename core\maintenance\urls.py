from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import MaintenanceViewSet, create_maintenance_update, get_maintenance_updates

router = DefaultRouter()
router.register(r'maintenance', MaintenanceViewSet, basename='maintenance')

urlpatterns = [
    path('', include(router.urls)),
    path('maintenance/<int:maintenance_id>/updates/', get_maintenance_updates, name='get-maintenance-updates'),
    path('maintenance/<int:maintenance_id>/create-update/', create_maintenance_update, name='create-maintenance-update'),
]





