from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Q
from .models import Maintenance, MaintenanceUpdate, MaintenanceImage, MaintenanceLog
from .serializers import MaintenanceSerializer, MaintenanceUpdateSerializer, MaintenanceLogSerializer
from rest_framework.pagination import PageNumberPagination
from django.utils.translation import gettext as _
from django.contrib.auth.models import User
from logs.utils import log_activity, log_error, log_data_change
from rest_framework.permissions import IsAuthenticated

# Async imports
from adrf.views import APIView as AsyncAPIView
from adrf.viewsets import ModelViewSet as AsyncModelViewSet
from adrf.decorators import api_view as async_api_view
from channels.db import database_sync_to_async
import asyncio
from asgiref.sync import sync_to_async


class CustomPageNumberPagination(PageNumberPagination):
    page_size_query_param = 'pageSize'
    page_query_param = 'page'

class MaintenanceViewSet(viewsets.ModelViewSet):
    queryset = Maintenance.objects.all()
    serializer_class = MaintenanceSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsAuthenticated]

    def create_log_entry(self, maintenance, user, change_type, old_value, new_value, description):
        MaintenanceLog.objects.create(
            maintenance=maintenance,
            user=user,
            change_type=change_type,
            old_value=old_value,
            new_value=new_value,
            description=description
        )

    def retrieve(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def list(self, request, *args, **kwargs):
        try:
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)

            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def perform_create(self, serializer):
        try:
            instance = serializer.save(created_by=self.request.user)
            log_activity(
                user=self.request.user,
                action='CREATE',
                model_name='Maintenance',
                record_id=instance.id,
                changes=serializer.validated_data,
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
        except Exception as e:
            log_error(
                level='ERROR',
                message=f'MaintenanceViewSet.perform_create() failed: {type(e).__name__}: {str(e)}',
                user=self.request.user,
                traceback=str(e),
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
            raise

    def perform_update(self, serializer):
        instance = self.get_object()
        old_data = {
            'status': instance.status,
            'priority': instance.priority,
            'assigned_to': list(instance.assigned_to.values_list('id', flat=True))
        }

        try:
            # Save the update
            updated_instance = serializer.save()

            # Log individual field changes
            for field, new_value in serializer.validated_data.items():
                if field in old_data and old_data[field] != new_value:
                    log_data_change(
                        model_instance=updated_instance,
                        field_name=field,
                        old_value=old_data[field],
                        new_value=new_value,
                        user=self.request.user,
                        ip_address=self.request.META.get('REMOTE_ADDR')
                    )

            # Log the overall activity
            log_activity(
                user=self.request.user,
                action='UPDATE',
                model_name='Maintenance',
                record_id=instance.id,
                changes=serializer.validated_data,
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
        except Exception as e:
            log_error(
                level='ERROR',
                message=f'MaintenanceViewSet.perform_update() failed: {type(e).__name__}: {str(e)}',
                user=self.request.user,
                traceback=str(e),
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
            raise

    @action(detail=False, methods=['get'])
    def filtered(self, request):
        queryset = self.get_queryset()

        # Handle user filters
        if created_by := request.query_params.get('createdByUsers'):
            queryset = queryset.filter(created_by=created_by)
        if assigned_to := request.query_params.get('assignedToUsers'):
            queryset = queryset.filter(assigned_to=assigned_to)
        if completed_by := request.query_params.get('completedByUsers'):
            queryset = queryset.filter(completed_by=completed_by)

        # Handle status, priority, and type filters
        if status := request.query_params.get('status'):
            queryset = queryset.filter(status=status)
        if priority := request.query_params.get('priority'):
            queryset = queryset.filter(priority=priority)
        if type_ := request.query_params.get('type'):
            queryset = queryset.filter(type=type_)

        # Handle date range filters
        for date_field, model_field in {
            'dateCreated': 'date_created',
            'dateDue': 'date_due',
            'dateCompleted': 'date_completed'
        }.items():
            if date_range := request.query_params.get(date_field):
                start_date, end_date = date_range.split(',')
                if start_date and end_date:
                    queryset = queryset.filter(**{
                        f'{model_field}__range': [start_date, end_date]
                    })

        # Handle asset filter
        if asset := request.query_params.get('assetParentAsset'):
            queryset = queryset.filter(
                Q(asset__icontains=asset) | Q(parent_asset__icontains=asset)
            )

        # Use pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            "count": queryset.count(),
            "next": None,
            "previous": None,
            "results": serializer.data
        })

    @action(detail=True, methods=['get'])
    def logs(self, request, pk=None):
        maintenance = self.get_object()
        logs = maintenance.logs.all()
        serializer = MaintenanceLogSerializer(logs, many=True)
        return Response(serializer.data)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_maintenance_update(request, maintenance_id):
    try:
        # Convert maintenance_id to int to ensure it's a valid number
        maintenance_id = int(maintenance_id)
        maintenance = Maintenance.objects.get(id=maintenance_id)

        # Create the update
        update = MaintenanceUpdate.objects.create(
            maintenance=maintenance,
            user=request.user,
            message=request.data.get('message', '')
        )

        # Handle multiple image uploads
        images = request.FILES.getlist('images')
        for image in images:
            MaintenanceImage.objects.create(
                update=update,
                image=image
            )

        serializer = MaintenanceUpdateSerializer(update)
        return Response(serializer.data)
    except (ValueError, TypeError):
        return Response(
            {'error': 'Invalid maintenance ID'},
            status=status.HTTP_400_BAD_REQUEST
        )
    except Maintenance.DoesNotExist:
        return Response(
            {'error': 'Maintenance record not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_maintenance_updates(request, maintenance_id):
    try:
        updates = MaintenanceUpdate.objects.filter(maintenance_id=maintenance_id).order_by('-timestamp')
        serializer = MaintenanceUpdateSerializer(updates, many=True)
        return Response(serializer.data)
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )

# ============================================================================
# ASYNC VIEWSETS FOR HIGH CONCURRENCY PRODUCTION USE
# ============================================================================

class AsyncMaintenanceViewSet(AsyncModelViewSet):
    """
    Async version of MaintenanceViewSet optimized for high concurrency.
    Handles multiple simultaneous maintenance operations without connection pool exhaustion.
    """
    queryset = Maintenance.objects.all()
    serializer_class = MaintenanceSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = []  # Disable for testing - enable in production: [IsAuthenticated]

    async def get_queryset(self):
        """Async version of get_queryset with optimized database access"""
        queryset = await database_sync_to_async(
            lambda: self.queryset.select_related('created_by', 'updated_by')
        )()

        # Apply filters asynchronously
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = await database_sync_to_async(
                lambda: queryset.filter(status=status_filter)
            )()

        priority = self.request.query_params.get('priority', None)
        if priority:
            queryset = await database_sync_to_async(
                lambda: queryset.filter(priority=priority)
            )()

        return queryset

    async def list(self, request, *args, **kwargs):
        """Async list method with improved error handling and connection management"""
        try:
            # Get queryset asynchronously
            queryset = await self.get_queryset()

            # Handle pagination asynchronously
            page = await database_sync_to_async(self.paginate_queryset)(queryset)

            if page is None:
                # No pagination - serialize all data
                queryset_list = await database_sync_to_async(list)(queryset)
                serializer = await database_sync_to_async(
                    lambda: self.get_serializer(queryset_list, many=True)
                )()
                serialized_data = await database_sync_to_async(lambda: serializer.data)()
                return Response(serialized_data)

            if not page:
                return Response([])

            # Serialize paginated data
            page_list = await database_sync_to_async(list)(page)
            serializer = await database_sync_to_async(
                lambda: self.get_serializer(page_list, many=True)
            )()
            serialized_data = await database_sync_to_async(lambda: serializer.data)()

            return await database_sync_to_async(
                lambda: self.get_paginated_response(serialized_data)
            )()

        except Exception as e:
            # Async error logging
            try:
                if hasattr(request, 'user') and request.user and request.user.is_authenticated:
                    await database_sync_to_async(log_error)(
                        level='ERROR',
                        message=f'AsyncMaintenanceViewSet.list() failed: {type(e).__name__}: {str(e)}',
                        user=request.user,
                        traceback=str(e),
                        ip_address=request.META.get('REMOTE_ADDR')
                    )
            except Exception:
                pass

            return Response([], status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    async def create(self, request, *args, **kwargs):
        """Async create method with improved connection handling"""
        try:
            # Create serializer and validate
            serializer = await database_sync_to_async(
                lambda: self.get_serializer(data=request.data)
            )()

            is_valid = await database_sync_to_async(
                lambda: serializer.is_valid(raise_exception=True)
            )()

            # Save the instance
            instance = await database_sync_to_async(serializer.save)()
            instance.created_by = request.user if hasattr(request, 'user') and request.user.is_authenticated else None
            instance.updated_by = request.user if hasattr(request, 'user') and request.user.is_authenticated else None
            await database_sync_to_async(instance.save)()

            # Create log entry asynchronously
            if hasattr(request, 'user') and request.user and request.user.is_authenticated:
                await database_sync_to_async(self.create_log_entry)(
                    instance, request.user, 'CREATE', None, 'Created', 'Maintenance record created'
                )

            # Get serialized data
            serialized_data = await database_sync_to_async(lambda: serializer.data)()

            # Log activity if user is authenticated
            try:
                if hasattr(request, 'user') and request.user and request.user.is_authenticated:
                    await database_sync_to_async(log_activity)(
                        user=request.user,
                        action='CREATE',
                        model_name='Maintenance',
                        record_id=serialized_data.get('id'),
                        changes=request.data,
                        ip_address=request.META.get('REMOTE_ADDR')
                    )
            except Exception:
                pass

            return Response(serialized_data, status=status.HTTP_201_CREATED)

        except Exception as e:
            # Async error logging
            try:
                if hasattr(request, 'user') and request.user and request.user.is_authenticated:
                    await database_sync_to_async(log_error)(
                        level='ERROR',
                        message=f'AsyncMaintenanceViewSet.create() failed: {type(e).__name__}: {str(e)}',
                        user=request.user,
                        traceback=str(e),
                        ip_address=request.META.get('REMOTE_ADDR')
                    )
            except Exception:
                pass

            return Response(
                {'error': 'Failed to create maintenance record', 'details': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def create_log_entry(self, maintenance, user, change_type, old_value, new_value, description):
        """Async version of create_log_entry"""
        await database_sync_to_async(MaintenanceLog.objects.create)(
            maintenance=maintenance,
            user=user,
            change_type=change_type,
            old_value=old_value,
            new_value=new_value,
            description=description
        )

    async def update(self, request, *args, **kwargs):
        """Async update method with improved connection handling"""
        try:
            partial = kwargs.pop('partial', False)
            instance = await database_sync_to_async(self.get_object)()
            old_status = instance.status

            # Update serializer
            serializer = await database_sync_to_async(
                lambda: self.get_serializer(instance, data=request.data, partial=partial)
            )()

            is_valid = await database_sync_to_async(
                lambda: serializer.is_valid(raise_exception=True)
            )()

            # Save the instance
            updated_instance = await database_sync_to_async(serializer.save)()
            updated_instance.updated_by = request.user if hasattr(request, 'user') and request.user.is_authenticated else None
            await database_sync_to_async(updated_instance.save)()

            # Create log entry for status change
            if old_status != updated_instance.status and hasattr(request, 'user') and request.user and request.user.is_authenticated:
                await database_sync_to_async(self.create_log_entry)(
                    updated_instance, request.user, 'UPDATE', old_status, updated_instance.status,
                    f'Status changed from {old_status} to {updated_instance.status}'
                )

            # Get serialized data
            serialized_data = await database_sync_to_async(lambda: serializer.data)()

            # Log activity if user is authenticated
            try:
                if hasattr(request, 'user') and request.user and request.user.is_authenticated:
                    await database_sync_to_async(log_activity)(
                        user=request.user,
                        action='UPDATE',
                        model_name='Maintenance',
                        record_id=serialized_data.get('id'),
                        changes=request.data,
                        ip_address=request.META.get('REMOTE_ADDR')
                    )
            except Exception:
                pass

            return Response(serialized_data)

        except Exception as e:
            # Async error logging
            try:
                if hasattr(request, 'user') and request.user and request.user.is_authenticated:
                    await database_sync_to_async(log_error)(
                        level='ERROR',
                        message=f'AsyncMaintenanceViewSet.update() failed: {type(e).__name__}: {str(e)}',
                        user=request.user,
                        traceback=str(e),
                        ip_address=request.META.get('REMOTE_ADDR')
                    )
            except Exception:
                pass

            return Response(
                {'error': 'Failed to update maintenance record', 'details': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )