from django.db import models
from django.contrib.auth import get_user_model
from django.utils.timezone import now

User = get_user_model()

class Maintenance(models.Model):
    STATUS_CHOICES = [
        ('New', 'New'),
        ('Assigned', 'Assigned'),
        ('In Progress', 'In Progress'),
        ('Waiting For Parts', 'Waiting For Parts'),
        ('Complete', 'Complete'),
    ]

    PRIORITY_CHOICES = [
        ('Low', 'Low'),
        ('Medium', 'Medium'),
        ('High', 'High'),
        ('Critical', 'Critical'),
    ]

    TYPE_CHOICES = [
        ('Breakdown', 'Breakdown'),
        ('Corrective', 'Corrective'),
        ('Inspection', 'Inspection'),
        ('Preventive', 'Preventive'),
        ('Movement', 'Movement'),
    ]

    code = models.CharField(max_length=50, unique=True)
    details = models.TextField()
    asset = models.CharField(max_length=255)
    parent_asset = models.CharField(max_length=255, blank=True, null=True)
    
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='New')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='Low')
    type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='Preventive')
    
    date_created = models.DateField(auto_now_add=True)
    date_scheduled = models.DateField(null=True, blank=True)
    date_due = models.DateField()
    date_completed = models.DateField(null=True, blank=True)
    
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_maintenances')
    assigned_to = models.ManyToManyField(
        User, 
        related_name='assigned_maintenances',
        blank=True
    )
    completed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='completed_maintenances')
    
    class Meta:
        ordering = ['-date_created']

    def __str__(self):
        return f"{self.code} - {self.asset}"

class MaintenanceUpdate(models.Model):
    maintenance = models.ForeignKey(Maintenance, related_name='updates', on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    message = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']

class MaintenanceImage(models.Model):
    update = models.ForeignKey(MaintenanceUpdate, related_name='images', on_delete=models.CASCADE)
    image = models.ImageField(upload_to='maintenance_updates/')
    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['uploaded_at']

class MaintenanceLog(models.Model):
    CHANGE_TYPES = (
        ('STATUS', 'Status Change'),
        ('ASSIGNMENT', 'Assignment Change'),
        ('PRIORITY', 'Priority Change'),
        ('OTHER', 'Other Change')
    )

    maintenance = models.ForeignKey('Maintenance', on_delete=models.CASCADE, related_name='logs')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    change_type = models.CharField(max_length=20, choices=CHANGE_TYPES)
    old_value = models.TextField(null=True, blank=True)
    new_value = models.TextField(null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    description = models.TextField()

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.maintenance.code} - {self.change_type} by {self.user.username}"
