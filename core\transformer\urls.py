from .views import *
from .views import get_inspection_analysis, process_transformer_offline
from rest_framework.routers import Default<PERSON>out<PERSON>
from django.urls import path, include

app_name = "transformer"

router = DefaultRouter()
# Synchronous ViewSets (original)
router.register(r'basestations', BasestationViewSet)
router.register(r'transformerdata', TransformerDataViewSet, basename='transformerdata')
router.register(r'populatedtransformerdata', PopulatedTransformerDataViewSet, basename='populatedtransformerdata')
router.register(r'inspections', InspectionViewSet, basename='inspections')
router.register(r'populatedInspection', PopulatedInspectionViewSet, basename='populatedInspection')
router.register(r'lvfeeders', LvFeederViewSet, basename='lvFeeder')
router.register(r'populatedlvfeeder', PopulatedLvFeederViewSet, basename='populatedLvFeeder')

# Async ViewSets for high concurrency (production-ready)
router.register(r'async/basestations', AsyncBasestationViewSet, basename='async_basestations')
router.register(r'async/transformerdata', AsyncTransformerDataViewSet, basename='async_transformerdata')
router.register(r'async/inspections', AsyncInspectionViewSet, basename='async_inspections')
router.register(r'async/lvfeeders', AsyncLvFeederViewSet, basename='async_lvfeeders')

urlpatterns = [
    # Synchronous endpoints (existing)
    path('', include(router.urls)),
    path('basestationsFiltered/', BasestationsFilteredAPIView.as_view(), name='basestations_filtered'),

    # Async endpoint for high concurrency (recommended for production)
    path('basestationsFilteredAsync/', AsyncBasestationsFilteredAPIView.as_view(), name='basestations_filtered_async'),

    path('create-inspection-with-feeders/', create_inspection_with_feeders, name='create_inspection_with_feeders'),
    path('transformer-analysis/<str:analysis_type>/', get_transformer_analysis, name='transformer_analysis'),
    path('inspection-analysis/<str:inspection_type>/', get_inspection_analysis, name='inspection_analysis'),
    path('lvfeeder-analysis/<str:analysis_type>/', get_lvfeeder_analysis, name='lvfeeder_analysis'),
    path('inspection-status/', inspection_status, name='inspection-status'),
    path('general-stats/', general_stats, name='general-stats'),

    # Concurrent endpoints (optimized for multiple simultaneous requests)
    path('concurrent/', include('transformer.concurrent_urls', namespace='concurrent_transformer')),
    path('process-transformer-offline/', process_transformer_offline, name='process_transformer_offline'),
    path('process-basestation-transformer-offline/', process_basestation_transformer_offline, name='process_transformer_offline'),

    path('nearest-basestation/', nearest_basestation, name='nearest-basestation'),
    path('export-excel/', export_transformer_data_excel, name='export-excel'),
    path('export-postgres/', export_postgres_backup, name='export-postgres'),


    path('dashboard/region/', region_dashboard, name='region_dashboard'),
    path('dashboard-statistics/', dashboard_statistics, name='dashboard_statistics'),
]
