"""
Async middleware for high concurrency production use.
Provides request rate limiting, connection management, and performance monitoring.
"""

import asyncio
import time
from django.core.cache import cache
from django.http import JsonResponse
from django.conf import settings
from channels.db import database_sync_to_async
import logging

logger = logging.getLogger(__name__)

class AsyncRateLimitMiddleware:
    """
    Async middleware to limit concurrent requests per user/IP to prevent resource exhaustion.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.max_concurrent = getattr(settings, 'ASYNC_MAX_CONCURRENT_REQUESTS', 100)
        self.timeout = getattr(settings, 'ASYNC_REQUEST_TIMEOUT', 300)

    async def __call__(self, scope, receive, send):
        # Only apply to HTTP requests
        if scope['type'] != 'http':
            return await self.get_response(scope, receive, send)
        
        # Get user identifier (IP address or user ID)
        client_ip = self.get_client_ip(scope)
        user_key = f"async_requests_{client_ip}"
        
        # Check current request count
        current_requests = cache.get(user_key, 0)
        
        if current_requests >= self.max_concurrent:
            # Too many concurrent requests
            response = JsonResponse({
                'error': 'Too many concurrent requests',
                'message': f'Maximum {self.max_concurrent} concurrent requests allowed',
                'retry_after': 30
            }, status=429)
            
            await send({
                'type': 'http.response.start',
                'status': 429,
                'headers': [
                    [b'content-type', b'application/json'],
                    [b'retry-after', b'30'],
                ],
            })
            await send({
                'type': 'http.response.body',
                'body': response.content,
            })
            return
        
        # Increment request count
        cache.set(user_key, current_requests + 1, timeout=self.timeout)
        
        try:
            # Process the request
            await self.get_response(scope, receive, send)
        finally:
            # Decrement request count
            current_requests = cache.get(user_key, 1)
            if current_requests > 1:
                cache.set(user_key, current_requests - 1, timeout=self.timeout)
            else:
                cache.delete(user_key)

    def get_client_ip(self, scope):
        """Extract client IP from ASGI scope"""
        headers = dict(scope.get('headers', []))
        
        # Check for forwarded IP first
        forwarded_for = headers.get(b'x-forwarded-for')
        if forwarded_for:
            return forwarded_for.decode().split(',')[0].strip()
        
        # Check for real IP
        real_ip = headers.get(b'x-real-ip')
        if real_ip:
            return real_ip.decode()
        
        # Fall back to client address
        client = scope.get('client')
        if client:
            return client[0]
        
        return 'unknown'

class AsyncPerformanceMiddleware:
    """
    Async middleware to monitor request performance and log slow requests.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.slow_request_threshold = 5.0  # Log requests taking more than 5 seconds

    async def __call__(self, scope, receive, send):
        # Only apply to HTTP requests
        if scope['type'] != 'http':
            return await self.get_response(scope, receive, send)
        
        start_time = time.time()
        path = scope.get('path', '')
        method = scope.get('method', 'GET')
        
        try:
            # Process the request
            await self.get_response(scope, receive, send)
        finally:
            # Calculate request duration
            duration = time.time() - start_time
            
            # Log slow requests
            if duration > self.slow_request_threshold:
                logger.warning(
                    f"Slow async request: {method} {path} took {duration:.2f}s"
                )
            
            # Log all requests in debug mode
            logger.debug(
                f"Async request: {method} {path} completed in {duration:.2f}s"
            )

class AsyncConnectionPoolMiddleware:
    """
    Async middleware to manage database connection pooling and prevent connection leaks.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    async def __call__(self, scope, receive, send):
        # Only apply to HTTP requests
        if scope['type'] != 'http':
            return await self.get_response(scope, receive, send)
        
        try:
            # Process the request
            await self.get_response(scope, receive, send)
        except Exception as e:
            # Log connection-related errors
            if 'connection' in str(e).lower() or 'pool' in str(e).lower():
                logger.error(f"Database connection error in async request: {e}")
            raise
        finally:
            # Ensure database connections are properly closed
            try:
                from django.db import connections
                await database_sync_to_async(connections.close_all)()
            except Exception as e:
                logger.warning(f"Error closing database connections: {e}")

class AsyncErrorHandlingMiddleware:
    """
    Async middleware for centralized error handling and logging.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    async def __call__(self, scope, receive, send):
        # Only apply to HTTP requests
        if scope['type'] != 'http':
            return await self.get_response(scope, receive, send)
        
        try:
            await self.get_response(scope, receive, send)
        except Exception as e:
            # Log the error
            path = scope.get('path', '')
            method = scope.get('method', 'GET')
            client_ip = self.get_client_ip(scope)
            
            logger.error(
                f"Async request error: {method} {path} from {client_ip} - {type(e).__name__}: {str(e)}"
            )
            
            # Return a generic error response
            error_response = JsonResponse({
                'error': 'Internal server error',
                'message': 'An error occurred while processing your request',
                'request_id': f"{int(time.time())}"
            }, status=500)
            
            await send({
                'type': 'http.response.start',
                'status': 500,
                'headers': [
                    [b'content-type', b'application/json'],
                ],
            })
            await send({
                'type': 'http.response.body',
                'body': error_response.content,
            })

    def get_client_ip(self, scope):
        """Extract client IP from ASGI scope"""
        headers = dict(scope.get('headers', []))
        
        # Check for forwarded IP first
        forwarded_for = headers.get(b'x-forwarded-for')
        if forwarded_for:
            return forwarded_for.decode().split(',')[0].strip()
        
        # Check for real IP
        real_ip = headers.get(b'x-real-ip')
        if real_ip:
            return real_ip.decode()
        
        # Fall back to client address
        client = scope.get('client')
        if client:
            return client[0]
        
        return 'unknown'

# Utility function to combine all async middleware
def get_async_middleware_stack():
    """
    Returns a list of async middleware classes for production use.
    """
    return [
        AsyncRateLimitMiddleware,
        AsyncPerformanceMiddleware,
        AsyncConnectionPoolMiddleware,
        AsyncErrorHandlingMiddleware,
    ]
