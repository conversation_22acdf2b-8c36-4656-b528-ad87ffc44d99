from django.shortcuts import render
from .serializers import *
from rest_framework import viewsets, filters
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.pagination import PageNumberPagination
from rest_framework import viewsets, status
from rest_framework.response import Response
import traceback
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from rest_framework.views import APIView
from decimal import Decimal

from django.db.models import OuterRef, Subquery

from logs.utils import log_activity, log_error, log_data_change
from rest_framework.exceptions import ValidationError
from logs.models import DataChangeLog

from datetime import datetime, timedelta
from django.db.models import Count, Q, Subquery, OuterRef
from django.db.models.functions import Coalesce
from django.db.models import Max
from .models import (
    TransformerData,
    Inspection,
    Basestation,
    LvFeeder
)

from rest_framework.decorators import api_view, permission_classes
from django.db import transaction

# Async imports
from adrf.views import APIView as AsyncAPIView
from adrf.decorators import api_view as async_api_view
from channels.db import database_sync_to_async
import asyncio
from asgiref.sync import sync_to_async


import uuid
import json
from django.utils import timezone
from .models import Basestation, TransformerData, Inspection, LvFeeder
from .serializers import TransformerDataSerializer

from decimal import Decimal, InvalidOperation
from math import radians, cos, sin, asin, sqrt
from django.db.models import Count, Case, When, Value, IntegerField

from django.http import HttpResponse
import openpyxl
from openpyxl.utils import get_column_letter
from django.core.cache import cache


from django.http import FileResponse
import subprocess
import os
import tempfile
from django.conf import settings

# Safe cache operations that handle Redis connection errors
def safe_cache_get(key, default=None):
    """Safely get from cache, return default if cache fails."""
    try:
        return cache.get(key, default)
    except Exception:
        return default

def safe_cache_set(key, value, timeout=300):
    """Safely set cache, continue silently if cache fails."""
    try:
        cache.set(key, value, timeout)
    except Exception:
        pass  # Continue without caching

def safe_cache_delete(key):
    """Safely delete from cache, continue silently if cache fails."""
    try:
        cache.delete(key)
    except Exception:
        pass  # Continue without deleting

# Create your views here.

class CustomPageNumberPagination(PageNumberPagination):
    # page_size = 2
    # page_size_query_param = 'page_size'
    # max_page_size = 100
    page_size_query_param = 'pageSize'
    page_query_param = 'page'


class BasestationViewSet(viewsets.ModelViewSet):
    queryset = Basestation.objects.all()
    serializer_class = BasestationSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsAuthenticated]
    lookup_field = 'station_code'

    def get_queryset(self):
        queryset = super().get_queryset()
        station_type = self.request.query_params.get('station_type', None)
        if station_type:
            queryset = queryset.filter(station_type=station_type)
        return queryset

    def list(self, request, *args, **kwargs):
        try:
            page = self.paginate_queryset(self.queryset)

            if page is None:
                serializer = self.get_serializer(self.queryset, many=True)
                return Response(serializer.data)

            if not page:
                return Response([])

            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        except Exception as e:
            log_error(
                level='ERROR',
                message=f'BasestationViewSet.list() failed: {type(e).__name__}: {str(e)}',
                user=request.user,
                traceback=str(e),
                ip_address=request.META.get('REMOTE_ADDR')
            )
            return Response([], status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def generate_station_code(self, csc_value, region_value):
        from account.models import CSCCenter, Region

        base_code = None
        try:
            csc = CSCCenter.objects.get(name=csc_value, region_id=region_value)
            base_code = csc.csc_code
        except CSCCenter.DoesNotExist:
            try:
                region = Region.objects.get(name=region_value)
                base_code = region.csc_code
            except Region.DoesNotExist:
                base_code = "UNKNOWN"

        last_station = Basestation.objects.filter(
            station_code__startswith=f"{base_code}-"
        ).order_by('-station_code').first()

        if last_station:
            try:
                last_id = int(last_station.station_code.split('-')[-1])
                new_id = last_id + 1
            except ValueError:
                new_id = 1
        else:
            new_id = 1

        return f"{base_code}-{new_id:04d}"

    def create(self, request, *args, **kwargs):
        try:
            csc = request.data.get('csc')
            region = request.data.get('region')
            substation = request.data.get('substation')
            feeder = request.data.get('feeder')
            address = request.data.get('address')
            gps_location = request.data.get('gps_location')


            if not csc or not region:
                raise ValidationError({"detail": "All 'csc', 'region', 'substation' and 'feeder' fields are required."})

            # Check for duplicate
            if Basestation.objects.filter(address=address, gps_location=gps_location).exists():
                raise ValidationError({"detail": "A base station with the same address and GPS location already exists."})


            mutable_data = request.data.copy()
            # First get regionId
            region_obj = Region.objects.filter(name=region).first()
            region_id = region_obj.csc_code if region_obj else None

            mutable_data.update({
                'regionId': region_id,
                'cscId': CSCCenter.objects.filter(name=csc, region=region_id).first().csc_code if CSCCenter.objects.filter(name=csc, region=region_id).exists() else None,
                'substationId': Substation.objects.filter(name=substation, region=region_id).first().id if Substation.objects.filter(name=substation, region=region_id).exists() else None,
                'feederId': Feeder.objects.filter(feeder_name=feeder, substation__name=substation).first().id if Feeder.objects.filter(feeder_name=feeder, substation__name=substation).exists() else None
            })

            station_code = self.generate_station_code(csc, region)

            mutable_data['station_code'] = station_code
            request._full_data = mutable_data

            serializer = self.get_serializer(data=mutable_data)
            serializer.is_valid(raise_exception=True)

            # Save with the user directly
            self.perform_create(serializer)
            instance = serializer.instance
            instance.created_by = request.user
            instance.updated_by = request.user
            instance.save()

            headers = self.get_success_headers(serializer.data)
            response = Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

            log_activity(
                user=request.user,
                action='CREATE',
                model_name='Basestation',
                record_id=response.data.get('station_code'),
                changes=mutable_data,
                ip_address=request.META.get('REMOTE_ADDR')
            )

            return response

        except Exception as e:
            log_error(
                level='ERROR',
                message=f'BasestationViewSet.create() failed: {type(e).__name__}: {str(e)}',
                user=request.user,
                traceback=str(e),
                ip_address=request.META.get('REMOTE_ADDR')
            )
            raise

    def perform_update(self, serializer):
        try:
            instance = self.get_object()
            print('instance_______________________________________________________________________', instance)
            old_data = {
                'region': instance.region,
                'csc': instance.csc,
                'substation': instance.substation,
                'feeder': instance.feeder,
                'station_type': instance.station_type,
                'address': instance.address,
                'gps_location': instance.gps_location,
            }

            print("old_data_________________________________", old_data)

            # Get the new values from validated_data
            region = serializer.validated_data.get('region', instance.region)
            csc = serializer.validated_data.get('csc', instance.csc)
            substation = serializer.validated_data.get('substation', instance.substation)
            feeder = serializer.validated_data.get('feeder', instance.feeder)
            station_type = serializer.validated_data.get('station_type', instance.station_type)

            # Get the actual model instances
            region_obj = Region.objects.filter(name=region).first()
            csc_obj = CSCCenter.objects.filter(name=csc, region=region_obj).first() if region_obj else None
            substation_obj = Substation.objects.filter(name=substation, region=region_obj).first() if region_obj else None
            feeder_obj = Feeder.objects.filter(feeder_name=feeder, substation__name=substation).first()

            # Store the original validated_data for logging
            log_data = serializer.validated_data.copy()

            # Update with model instances
            serializer.validated_data.update({
                'regionId': region_obj,
                'cscId': csc_obj,
                'substationId': substation_obj,
                'feederId': feeder_obj
            })

            updated_instance = serializer.save(updated_by=self.request.user)

            # Log changes using the original data
            for field, new_value in log_data.items():
                if field in old_data and old_data[field] != new_value:
                    log_data_change(
                        model_instance=updated_instance,
                        field_name=field,
                        old_value=old_data[field],
                        new_value=new_value,
                        user=self.request.user,
                        reason=self.request.data.get('reason'),
                        ip_address=self.request.META.get('REMOTE_ADDR')
                    )

            # Log activity with serializable data
            log_activity(
                user=self.request.user,
                action='UPDATE',
                model_name='Basestation',
                record_id=instance.station_code,
                changes={
                    'region': region,
                    'csc': csc,
                    'substation': substation,
                    'feeder': feeder,
                    'station_type': station_type,
                    'address': serializer.validated_data.get('address', instance.address),
                    'gps_location': serializer.validated_data.get('gps_location', instance.gps_location),
                },
                ip_address=self.request.META.get('REMOTE_ADDR')
            )

            return updated_instance

        except Exception as e:
            log_error(
                level='ERROR',
                message=f'BasestationViewSet.update() failed: {type(e).__name__}: {str(e)}',
                user=self.request.user,
                traceback=str(e),
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
            raise

    def destroy(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            station_code = instance.station_code

            # Capture complete record data before deletion for potential restoration
            serializer = BasestationSerializer(instance)
            complete_record_data = serializer.data

            self.perform_destroy(instance)

            log_activity(
                user=request.user,
                action='DELETE',
                model_name='Basestation',
                record_id=station_code,
                changes={
                    'deleted_record': complete_record_data,
                    'station_code': station_code,
                    'deletion_timestamp': timezone.now().isoformat()
                },
                ip_address=request.META.get('REMOTE_ADDR')
            )

            return Response(status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            log_error(
                level='ERROR',
                message=f'BasestationViewSet.destroy() failed: {type(e).__name__}: {str(e)}',
                user=request.user,
                traceback=str(e),
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
            raise

    @action(detail=False, methods=['POST'])
    def bulk_delete(self, request):
        """
        Bulk delete basestations by station codes.
        Expects: { "station_codes": ["code1", "code2", ...] }
        """
        try:
            station_codes = request.data.get('station_codes', [])

            if not station_codes:
                return Response(
                    {'error': 'station_codes list is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not isinstance(station_codes, list):
                return Response(
                    {'error': 'station_codes must be a list'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get basestations that exist
            existing_basestations = Basestation.objects.filter(station_code__in=station_codes)
            existing_codes = list(existing_basestations.values_list('station_code', flat=True))

            if not existing_codes:
                return Response(
                    {'error': 'No basestations found with the provided station codes'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Capture complete record data before deletion for potential restoration
            serializer = BasestationSerializer(existing_basestations, many=True)
            complete_records_data = serializer.data

            # Delete the basestations
            deleted_count, _ = existing_basestations.delete()

            # Log the bulk delete activity (only if user is authenticated)
            if request.user.is_authenticated:
                log_activity(
                    user=request.user,
                    action='DELETE',
                    model_name='Basestation',
                    record_id=f"bulk_delete_{len(existing_codes)}_items",
                    changes={
                        'deleted_records': complete_records_data,
                        'deleted_station_codes': existing_codes,
                        'requested_station_codes': station_codes,
                        'deleted_count': deleted_count,
                        'deletion_timestamp': timezone.now().isoformat()
                    },
                    ip_address=request.META.get('REMOTE_ADDR')
                )

            # Find codes that were not found
            not_found_codes = [code for code in station_codes if code not in existing_codes]

            response_data = {
                'message': f'Successfully deleted {deleted_count} basestations',
                'deleted_count': deleted_count,
                'deleted_station_codes': existing_codes
            }

            if not_found_codes:
                response_data['not_found_station_codes'] = not_found_codes
                response_data['message'] += f'. {len(not_found_codes)} station codes were not found.'

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            if request.user.is_authenticated:
                log_error(
                    level='ERROR',
                    message=f'BasestationViewSet.bulk_delete() failed: {type(e).__name__}: {str(e)}',
                    user=request.user,
                    traceback=str(e),
                    ip_address=request.META.get('REMOTE_ADDR')
                )
            return Response(
                {'error': 'Failed to bulk delete basestations', 'details': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'], url_path='changes')
    def basestation_changes(self, request, station_code=None):
        try:
            # Get current transformers in this basestation
            current_transformers = TransformerData.objects.filter(
                basestation__station_code=station_code
            ).values('id', 'serial_number')

            # Get historical movements for both basestation and feeder
            queryset = DataChangeLog.objects.filter(
                Q(model_name='Transformer Data') &
                (
                    # Basestation changes
                    (Q(field_name='basestation') &
                    (Q(old_value=station_code) | Q(new_value=station_code)))
                ) |
                Q(model_name='Basestation') &
                (
                    # Feeder changes
                    Q(field_name='feeder') & Q(record_id=  "BS-"+station_code)
                ) |
                Q(model_name='Basestation') &
                (
                    # Feeder changes
                    Q(field_name='substation') & Q(record_id=  "BS-"+station_code)
                )
            ).order_by('-timestamp')

            # Get pagination parameters
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))

            # Calculate start and end indices
            start = (page - 1) * page_size
            end = start + page_size

            # Slice the queryset
            paginated_queryset = queryset[start:end]

            # Prepare the historical movements data
            movements = []
            for log in paginated_queryset:
                print("LOG_______________________", log.model_name, log.field_name)
                if log.model_name == 'Transformer Data':
                    movement_type = "moved_in" if log.new_value == station_code else "moved_out"
                    location_type = "basestation"
                    transformer_id = log.record_id
                elif log.model_name == 'Basestation':
                    movement_type = f"{log.field_name}_change"
                    location_type = log.field_name
                    transformer_id = None

                movements.append({
                    'id': log.id,
                    'timestamp': log.timestamp,
                    'transformer_id': transformer_id,
                    'movement_type': movement_type,
                    'location_type': location_type,
                    'old_location': log.old_value or "None",
                    'new_location': log.new_value or "None",
                    'reason': log.reason,
                    'changed_by': log.changed_by.username if log.changed_by else 'System'
                })

            response_data = {
                'current_transformers': list(current_transformers),
                'movements': {
                    'count': queryset.count(),
                    'next': page * page_size < queryset.count(),
                    'previous': page > 1,
                    'results': movements
                }
            }

            return Response(response_data)
        except Exception as e:
            log_error(
                level='ERROR',
                message=f'get_basestation_changes() failed: {type(e).__name__}: {str(e)}',
                user=request.user,
                traceback=str(e)
            )
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_by_station_code(self, request):
        station_code = request.query_params.get('station_code')

        print("station_code", station_code)
        if not station_code:
            return Response({'error': 'Station code is required'}, status=400)

        try:
            # Search in Basestation model
            queryset = Basestation.objects.filter(
                station_code__icontains=station_code
            )

            print("queryset", queryset)

            # Serialize the results
            serializer = BasestationSerializer(queryset, many=True)
            return Response(serializer.data)

        except Exception as e:
            return Response({'error': str(e)}, status=500)


class TransformerDataViewSet(viewsets.ModelViewSet):
    queryset = TransformerData.objects.all()
    serializer_class = TransformerDataSerializer
    pagination_class = CustomPageNumberPagination
    # permission_classes = [IsAuthenticated]

    def get_queryset(self):
        print("self.request.user", self.request.user)
        queryset = super().get_queryset()
        service_type = self.request.query_params.get('service_type', None)
        status = self.request.query_params.get('status', None)

        if service_type:
            queryset = queryset.filter(service_type=service_type)
        if status:
            queryset = queryset.filter(status=status)
        print("queryset", queryset)
        return queryset

    def _prepare_changes_data(self, data):
        """Helper method to prepare changes data for logging"""
        changes = {}
        for key, value in data.items():
            if hasattr(value, '_meta'):  # If it's a model instance
                if isinstance(value, Basestation):
                    changes[key] = value.station_code
                else:
                    changes[key] = str(value.pk)
            else:
                changes[key] = value
        return changes

    def perform_create(self, serializer):
        try:
            print(self.request.query_params.get('basestation', None))
            instance = serializer.save(
                created_by=self.request.user,
                updated_by=self.request.user
            )

            # Prepare serializable changes data
            changes = self._prepare_changes_data(serializer.validated_data)

            log_activity(
                user=self.request.user,
                action='CREATE',
                model_name='TransformerData',
                record_id=instance.id,
                changes=changes,
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
        except Exception as e:
            log_error(
                level='ERROR',
                message=f'TransformerDataViewSet.create() failed: {type(e).__name__}: {str(e)}',
                user=self.request.user,
                traceback=str(e),
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
            raise

    def perform_update(self, serializer):
        try:
            print("serializer.validated_data")
            instance = self.get_object()

            print("instance", instance)
            # Store old values before update
            old_data = {
                'trafo_type': instance.trafo_type,
                'capacity': instance.capacity,
                'dt_number': instance.dt_number,
                'primary_voltage': instance.primary_voltage,
                'colling_type': instance.colling_type,
                'serial_number': instance.serial_number,
                'manufacturer': instance.manufacturer,
                'vector_group': instance.vector_group,
                'impedance_voltage': instance.impedance_voltage,
                'winding_weight': instance.winding_weight,
                'oil_weight': instance.oil_weight,
                'year_of_manufacturing': instance.year_of_manufacturing if instance.year_of_manufacturing else None,
                'date_of_installation': instance.date_of_installation if instance.date_of_installation else None,
                'basestation': instance.basestation.station_code if instance.basestation else None,
                'service_type': instance.service_type,
                'status': instance.status,
            }

            basestation_value = serializer.validated_data.get('basestation', None)
            if basestation_value is not None:
                from .models import Basestation
                if isinstance(basestation_value, str):
                    try:
                        basestation_obj = Basestation.objects.get(station_code=basestation_value)
                        serializer.validated_data['basestation'] = basestation_obj
                    except Basestation.DoesNotExist:
                        from rest_framework.exceptions import ValidationError
                        raise ValidationError({'error': f'Basestation with station_code "{basestation_value}" does not exist.'})
                elif not isinstance(basestation_value, Basestation):
                    from rest_framework.exceptions import ValidationError
                    raise ValidationError({'error': 'Invalid basestation value.'})

            # Save the update
            updated_instance = serializer.save(updated_by=self.request.user)

            # Prepare serializable changes data
            changes = self._prepare_changes_data(serializer.validated_data)

            if 'capacity' in serializer.validated_data and old_data['capacity'] != serializer.validated_data['capacity']:
                try:
                    from decimal import Decimal, InvalidOperation
                    capacity = serializer.validated_data['capacity']
                    if capacity is not None:
                        try:
                            capacity = Decimal(str(capacity))
                        except (InvalidOperation, ValueError):
                            capacity = None
                    lvfeeders = LvFeeder.objects.filter(inspection_data__transformer_data=updated_instance)
                    for feeder in lvfeeders:
                        try:
                            R = Decimal(str(feeder.R_load_current or 0))
                            S = Decimal(str(feeder.S_load_current or 0))
                            T = Decimal(str(feeder.T_load_current or 0))
                            Iavg = (R + S + T) / 3 if (R and S and T) else Decimal('0')
                            Iconst = capacity / (Decimal('3') ** Decimal('0.5') * Decimal('0.4')) if capacity and capacity != 0 else Decimal('0')
                            transformer_load = (Iavg / Iconst) * 100 if Iconst != 0 else Decimal('0')
                            feeder.transformer_load = round(transformer_load, 2)
                            feeder.save()
                        except Exception as feeder_calc_error:
                            # Optionally log error
                            print(f"Error recalculating transformer_load for feeder {feeder.id}: {feeder_calc_error}")
                    # Optionally update inspection total_transformer_load
                    inspections = Inspection.objects.filter(transformer_data=updated_instance)
                    for inspection in inspections:
                        update_inspection_total_load(inspection.id)
                except Exception as recalc_error:
                    print(f"Error recalculating transformer loads after capacity change: {recalc_error}")

            # Log individual field changes
            for field, new_value in changes.items():
                if field in old_data and old_data[field] != new_value:
                    log_data_change(
                        model_instance=updated_instance,
                        field_name=field,
                        old_value=old_data[field],
                        new_value=new_value,
                        user=self.request.user,
                        reason=self.request.data.get('reason'),
                        ip_address=self.request.META.get('REMOTE_ADDR')
                    )

            # Log the overall activity
            log_activity(
                user=self.request.user,
                action='UPDATE',
                model_name='TransformerData',
                record_id=instance.id,
                changes=changes,
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
        except Exception as e:
            log_error(
                level='ERROR',
                message=f'TransformerDataViewSet.update() failed: {type(e).__name__}: {str(e)}',
                user=self.request.user,
                traceback=str(e),
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
            # Return error response instead of raising
            return Response(
                {'error': 'Failed to update transformer data', 'details': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def destroy(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            instance_id = instance.id

            # Capture complete record data before deletion for potential restoration
            serializer = TransformerDataSerializer2(instance)  # Use detailed serializer with basestation data
            complete_record_data = serializer.data

            instance.delete()

            log_activity(
                user=request.user,
                action='DELETE',
                model_name='TransformerData',
                record_id=instance_id,
                changes={
                    'deleted_record': complete_record_data,
                    'id': instance_id,
                    'deletion_timestamp': timezone.now().isoformat()
                },
                ip_address=request.META.get('REMOTE_ADDR')
            )
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            log_error(
                level='ERROR',
                message=f'TransformerDataViewSet.destroy() failed: {type(e).__name__}: {str(e)}',
                user=request.user,
                traceback=str(e),
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
            raise

    @action(detail=False, methods=['POST'])
    def bulk_delete(self, request):
        """
        Bulk delete transformers by IDs.
        Expects: { "transformer_ids": [1, 2, 3, ...] }

        """
        try:
            transformer_ids = request.data.get('transformer_ids', [])

            if not transformer_ids:
                return Response(
                    {'error': 'transformer_ids list is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not isinstance(transformer_ids, list):
                return Response(
                    {'error': 'transformer_ids must be a list'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate that all IDs are integers
            try:
                transformer_ids = [int(id) for id in transformer_ids]
            except (ValueError, TypeError):
                return Response(
                    {'error': 'All transformer_ids must be valid integers'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get transformers that exist
            existing_transformers = TransformerData.objects.filter(id__in=transformer_ids)
            existing_ids = list(existing_transformers.values_list('id', flat=True))

            if not existing_ids:
                return Response(
                    {'error': 'No transformers found with the provided IDs'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Capture complete record data before deletion for potential restoration
            serializer = TransformerDataSerializer2(existing_transformers, many=True)  # Use detailed serializer
            complete_records_data = serializer.data

            # Delete the transformers
            deleted_count, _ = existing_transformers.delete()

            # Log the bulk delete activity (only if user is authenticated)
            if request.user.is_authenticated:
                log_activity(
                    user=request.user,
                    action='DELETE',
                    model_name='TransformerData',
                    record_id=f"bulk_delete_{len(existing_ids)}_items",
                    changes={
                        'deleted_records': complete_records_data,
                        'deleted_transformer_ids': existing_ids,
                        'requested_transformer_ids': transformer_ids,
                        'deleted_count': deleted_count,
                        'deletion_timestamp': timezone.now().isoformat()
                    },
                    ip_address=request.META.get('REMOTE_ADDR')
                )

            # Find IDs that were not found
            not_found_ids = [id for id in transformer_ids if id not in existing_ids]

            response_data = {
                'message': f'Successfully deleted {deleted_count} transformers',
                'deleted_count': deleted_count,
                'deleted_transformer_ids': existing_ids
            }

            if not_found_ids:
                response_data['not_found_transformer_ids'] = not_found_ids
                response_data['message'] += f'. {len(not_found_ids)} transformer IDs were not found.'

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            if request.user.is_authenticated:
                log_error(
                    level='ERROR',
                    message=f'TransformerDataViewSet.bulk_delete() failed: {type(e).__name__}: {str(e)}',
                    user=request.user,
                    traceback=str(e),
                    ip_address=request.META.get('REMOTE_ADDR')
                )
            return Response(
                {'error': 'Failed to bulk delete transformers', 'details': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

# TransformerData with Base Station populated
class PopulatedTransformerDataViewSet(viewsets.ModelViewSet):
    queryset = TransformerData.objects.all()
    serializer_class = TransformerDataSerializer2
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsAuthenticated]


class InspectionViewSet(viewsets.ModelViewSet):
    queryset = Inspection.objects.all()
    serializer_class = InspectionSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        transformer_id = self.request.query_params.get('transformer_data')
        if transformer_id:
            queryset = queryset.filter(transformer_data_id=transformer_id)
        return queryset

    def perform_create(self, serializer):
        try:
            instance = serializer.save(
                created_by=self.request.user,
                updated_by=self.request.user
            )

            RS = instance.R_S_Voltage or 0
            RT = instance.R_T_Voltage or 0
            ST = instance.T_S_Voltage or 0

            if RS and RT and ST:
                max_diff = max(abs(RS-RT), abs(RS-ST), abs(RT-ST))
                avg_voltage = (RS + RT + ST) / 3
                voltage_phase_unbalance = (max_diff / avg_voltage) * 100 if avg_voltage != 0 else 0
                average_voltage = avg_voltage
            else:
                voltage_phase_unbalance = 0
                average_voltage = 0

            instance.voltage_phase_unbalance = voltage_phase_unbalance
            instance.average_voltage = average_voltage
            instance.save()

            # Prepare the changes data for logging
            changes = serializer.validated_data.copy()
            # Convert TransformerData instance to its ID for JSON serialization
            if 'transformer_data' in changes:
                changes['transformer_data'] = changes['transformer_data'].id


            log_activity(
                user=self.request.user,
                action='CREATE',
                model_name='Inspection',
                record_id=instance.id,
                changes=changes,
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
        except Exception as e:
            log_error(
                level='ERROR',
                message=f'InspectionViewSet.create() failed: {type(e).__name__}: {str(e)}',
                user=self.request.user,
                traceback=str(e),
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
            raise

    def perform_update(self, serializer):
        try:
            instance = self.get_object()
            old_data = {
                'body_condition': instance.body_condition,
                'arrester': instance.arrester,
                'drop_out': instance.drop_out,
                'fuse_link': instance.fuse_link,
                'mv_bushing': instance.mv_bushing,
                'mv_cable_lug': instance.mv_cable_lug,
                'lv_bushing': instance.lv_bushing,
                'lv_cable_lug': instance.lv_cable_lug,
                'oil_level': instance.oil_level,
                'insulation_level': instance.insulation_level,
                'horn_gap': instance.horn_gap,
                'silica_gel': instance.silica_gel,
                'has_linkage': instance.has_linkage,
                'arrester_body_ground': instance.arrester_body_ground,
                'neutral_ground': instance.neutral_ground,
                'status_of_mounting': instance.status_of_mounting,
                'mounting_condition': instance.mounting_condition,
                'N_load_current': instance.N_load_current,
                'R_S_Voltage': instance.R_S_Voltage,
                'R_T_Voltage': instance.R_T_Voltage,
                'T_S_Voltage': instance.T_S_Voltage,
                'transformer_data': instance.transformer_data.id if instance.transformer_data else None,
            }

            updated_instance = serializer.save(updated_by=self.request.user)

            # Use updated_instance for new values
            RS = updated_instance.R_S_Voltage or 0
            RT = updated_instance.R_T_Voltage or 0
            ST = updated_instance.T_S_Voltage or 0

            if RS and RT and ST:
                max_diff = max(abs(RS-RT), abs(RS-ST), abs(RT-ST))
                avg_voltage = (RS + RT + ST) / 3
                voltage_phase_unbalance = (max_diff / avg_voltage) * 100 if avg_voltage != 0 else 0
                average_voltage = avg_voltage
            else:
                voltage_phase_unbalance = 0
                average_voltage = 0

            updated_instance.voltage_phase_unbalance = voltage_phase_unbalance
            updated_instance.average_voltage = average_voltage
            updated_instance.save()

            # Prepare the changes data for logging
            changes = serializer.validated_data.copy()
            from .serializers import InspectionSerializer
            print("show all instances", InspectionSerializer(updated_instance).data)
            if 'transformer_data' in changes:
                changes['transformer_data'] = changes['transformer_data'].id

            for field, new_value in changes.items():
                if field in old_data and old_data[field] != new_value:
                    # Convert Decimal values to strings for JSON serialization
                    if isinstance(new_value, Decimal):
                        new_value = str(new_value)

                    log_data_change(
                        model_instance=updated_instance,
                        field_name=field,
                        old_value=old_data[field],
                        new_value=new_value,
                        reason=self.request.data.get('reason'),
                        user=self.request.user,
                        ip_address=self.request.META.get('REMOTE_ADDR')
                    )

            log_activity(
                user=self.request.user,
                action='UPDATE',
                model_name='Inspection',
                record_id=updated_instance.id,
                changes=changes,
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
        except Exception as e:
            log_error(
                level='ERROR',
                message=f'InspectionViewSet.update() failed: {type(e).__name__}: {str(e)}',
                user=self.request.user,
                traceback=str(e),
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
            raise

class PopulatedInspectionViewSet(viewsets.ModelViewSet):
    queryset = Inspection.objects.all()
    serializer_class = InspectionSerializer2
    pagination_class = CustomPageNumberPagination

class LvFeederViewSet(viewsets.ModelViewSet):
    queryset = LvFeeder.objects.all()
    serializer_class = LvFeederSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        inspection_id = self.request.query_params.get('inspection_data')
        if inspection_id:
            queryset = queryset.filter(inspection_data_id=inspection_id)
        return queryset

    def perform_create(self, serializer):
        try:
            instance = serializer.save(
                created_by=self.request.user,
                updated_by=self.request.user
            )

            R = serializer.validated_data.get('R_load_current', 0)
            S = serializer.validated_data.get('S_load_current', 0)
            T = serializer.validated_data.get('T_load_current', 0)
            N = instance.inspection_data.N_load_current

            transformer_data = instance.inspection_data.transformer_data
            capacity = float(transformer_data.capacity) if transformer_data.capacity else 0
            Iconst = capacity / (3 ** 0.5 * 0.4) if capacity != 0 else 0
            R, S, T = float(R or 0), float(S or 0), float(T or 0)
            Iavg = (R + S + T) / 3 if (R and S and T) else 0

            transformer_load = (Iavg / Iconst) * 100 if Iconst != 0 else 0
            current_phase_unbalance = (max(abs(R-S), abs(R-T), abs(S-T)) / Iavg) * 100 if Iavg != 0 else 0
            N = float(N or 0)
            percentage_of_neutral = (N / Iavg) * 100 if Iavg != 0 else 0

            instance.transformer_load = transformer_load
            instance.current_phase_unbalance = current_phase_unbalance
            instance.percentage_of_neutral = percentage_of_neutral
            instance.save()

            # Update the total transformer load in the associated inspection
            update_inspection_total_load(instance.inspection_data_id)

            # Prepare the changes data for logging by converting model instances to IDs
            changes = serializer.validated_data.copy()
            if 'inspection_data' in changes:
                changes['inspection_data'] = changes['inspection_data'].id

            # Convert any Decimal values to strings
            for key, value in changes.items():
                if isinstance(value, Decimal):
                    changes[key] = str(value)

            log_activity(
                user=self.request.user,
                action='CREATE',
                model_name='LvFeeder',
                record_id=instance.id,
                changes=changes,
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
        except Exception as e:
            log_error(
                level='ERROR',
                message=f'LVFeederViewSet.create() failed: {type(e).__name__}: {str(e)}',
                user=self.request.user,
                traceback=str(e),
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
            raise

    def perform_update(self, serializer):
        try:
            instance = self.get_object()
            # Separate numeric fields from other fields
            old_data = {
                'distribution_box_name': instance.distribution_box_name,
                'inspection_data': instance.inspection_data.id if instance.inspection_data else None,
            }

            # Handle numeric fields separately
            numeric_fields = {
                'R_load_current': str(instance.R_load_current) if instance.R_load_current else None,
                'S_load_current': str(instance.S_load_current) if instance.S_load_current else None,
                'T_load_current': str(instance.T_load_current) if instance.T_load_current else None,
                'R_fuse_rating': str(instance.R_fuse_rating) if instance.R_fuse_rating else None,
                'S_fuse_rating': str(instance.S_fuse_rating) if instance.S_fuse_rating else None,
                'T_fuse_rating': str(instance.T_fuse_rating) if instance.T_fuse_rating else None,
            }

            updated_instance = serializer.save(updated_by=self.request.user)

            R = float(serializer.validated_data.get('R_load_current') or 0)
            S = float(serializer.validated_data.get('S_load_current') or 0)
            T = float(serializer.validated_data.get('T_load_current') or 0)
            N = float(instance.inspection_data.N_load_current or 0)

            transformer_data = instance.inspection_data.transformer_data
            capacity = float(transformer_data.capacity) if transformer_data.capacity else 0
            Iconst = capacity / (3 ** 0.5 * 0.4) if capacity != 0 else 0
            R, S, T = float(R or 0), float(S or 0), float(T or 0)
            Iavg = (R + S + T) / 3 if (R and S and T) else 0

            transformer_load = (Iavg / Iconst) * 100 if Iconst != 0 else 0
            current_phase_unbalance = (max(abs(R-S), abs(R-T), abs(S-T)) / Iavg) * 100 if Iavg != 0 else 0
            N = float(N or 0)
            percentage_of_neutral = (N / Iavg) * 100 if Iavg != 0 else 0

            instance.transformer_load = transformer_load
            instance.current_phase_unbalance = current_phase_unbalance
            instance.percentage_of_neutral = percentage_of_neutral
            instance.save()

            # Update the total transformer load in the associated inspection
            update_inspection_total_load(updated_instance.inspection_data_id)

            # Prepare the changes data for logging
            changes = serializer.validated_data.copy()
            if 'inspection_data' in changes:
                changes['inspection_data'] = changes['inspection_data'].id

            # Log changes for regular fields
            for field, new_value in changes.items():
                if field in old_data and old_data[field] != new_value:
                    log_data_change(
                        model_instance=updated_instance,
                        field_name=field,
                        old_value=old_data[field],
                        new_value=new_value,
                        reason=self.request.data.get('reason'),
                        user=self.request.user,
                        ip_address=self.request.META.get('REMOTE_ADDR')
                    )
                # Only log numeric fields if they actually changed
                elif field in numeric_fields:
                    new_value_str = str(new_value) if new_value is not None else None
                    if numeric_fields[field] != new_value_str:
                        log_data_change(
                            model_instance=updated_instance,
                            field_name=field,
                            old_value=numeric_fields[field],
                            new_value=new_value_str,
                            reason=self.request.data.get('reason'),
                            user=self.request.user,
                            ip_address=self.request.META.get('REMOTE_ADDR')
                        )

            log_activity(
                user=self.request.user,
                action='UPDATE',
                model_name='LvFeeder',
                record_id=instance.id,
                changes=changes,
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
        except Exception as e:
            log_error(
                level='ERROR',
                message=f'LVFeederViewSet.update() failed: {type(e).__name__}: {str(e)}',
                user=self.request.user,
                traceback=str(e),
                ip_address=self.request.META.get('REMOTE_ADDR')
            )
            raise

    def perform_destroy(self, instance):
        # Store the inspection ID before deleting the instance
        inspection_id = instance.inspection_data_id

        # Delete the instance
        instance.delete()

        # Update the total transformer load in the associated inspection
        update_inspection_total_load(inspection_id)

class PopulatedLvFeederViewSet(viewsets.ModelViewSet):
    queryset = LvFeeder.objects.all()
    serializer_class = LvFeederSerializer2
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsAuthenticated]





class BasestationsFilteredAPIView(APIView):

    # permission_classes = [IsAuthenticated]

    FILTER_CONFIG = {
        'BaseStation': {
            'model': Basestation,
            'serializer': BasestationSerializer,
            'fields': {
                'station_code': 'station_code__iexact',
                'substation': 'substation__icontains',
                'feeder': 'feeder__icontains',
                'address': 'address__icontains',
                'gps_location': 'gps_location__icontains',
                'region': 'region__icontains',
                'csc': 'csc__icontains',
                "station_type": 'station_type__iexact',
                'created_by': 'created_by__username__icontains',
                'updated_by': 'updated_by__username__icontains',
            }
        },
        'Transformer': {
            'model': TransformerData,
            'serializer': TransformerDataSerializer2,
            'fields': {
                'id': 'id__exact',
                'trafo_type': 'trafo_type__iexact',
                'capacity': 'capacity__iexact',
                'primary_voltage': 'primary_voltage__exact',
                'colling_type': 'colling_type__iexact',
                'manufacturer': 'manufacturer__iexact',
                'vector_group': 'vector_group__iexact',
                'year_of_manufacturing': 'year_of_manufacturing__range',

                'dt_number': 'dt_number__icontains',
                'serial_number': 'serial_number__icontains',
                'status': 'status__iexact',
                'service_type': 'service_type__iexact',
                'impedance_voltage': 'impedance_voltage__iexact',
                'winding_weight': 'winding_weight__iexact',
                'oil_weight': 'oil_weight__iexact',

                'station_code': 'basestation__station_code__iexact',
                'region': 'basestation__region__icontains',
                'csc': 'basestation__csc__icontains',
                'created_by': 'created_by__username__icontains',
                'updated_by': 'updated_by__username__icontains',

            }
        },
        'Inspection': {
            'model': Inspection,
            'serializer': InspectionSerializer2,
            'fields': {
                'id': 'id__exact',
                'ServiceType': 'service_type__iexact',
                'BodyCondition': 'body_condition__iexact',
                'Arrester': 'arrester__iexact',
                'DropOutFuse': 'drop_out__iexact',
                'FuseLink': 'fuse_link__iexact',
                'MVBushing': 'mv_bushing__iexact',
                'MvCableLug': 'mv_cable_lug__iexact',
                'LVBushing': 'lv_bushing__iexact',
                'LvCableLug': 'lv_cable_lug__iexact',
                'OilLevel': 'oil_level__iexact',
                'InsulationLevel': 'insulation_level__iexact',
                'HornGap': 'horn_gap__iexact',
                'silica_gel ': 'silica_gel__iexact',
                'HasLinkage': 'has_linkage__iexact',
                'ArresterBodyGround': 'arrester_body_ground__iexact',
                'NeutralGround': 'neutral_ground__iexact',
                'StatusOfMounting': 'status_of_mounting__iexact',
                'MountingCondition': 'mounting_condition__iexact',
                'Owner': 'owner__icontains',
                'NLoadCurrent': 'N_load_current__icontains',
                'RSVoltage': 'R_S_Voltage__icontains',
                'RTVoltage': 'R_T_Voltage__icontains',
                'TSVoltage': 'T_S_Voltage__icontains',
                'station_code': 'transformer_data__basestation__station_code__iexact',
                'region': 'transformer_data__basestation__region__icontains',
                'csc': 'transformer_data__basestation__csc__icontains',
                'created_by': 'created_by__username__icontains',
                'updated_by': 'updated_by__username__icontains',
            }
        },
        'LatestInspection': {
            'model': Inspection,
            'serializer': InspectionSerializer2,
            'queryset': lambda: Inspection.objects.filter(
                id__in=Subquery(
                    Inspection.objects.filter(
                        transformer_data=OuterRef('transformer_data')
                    ).order_by('-created_at').values('id')[:1]
                )
            ),
            'fields': {
                'id': 'id__exact',
                'ServiceType': 'service_type__iexact',
                'BodyCondition': 'body_condition__iexact',
                'Arrester': 'arrester__iexact',
                'DropOutFuse': 'drop_out__iexact',
                'FuseLink': 'fuse_link__iexact',
                'MvBushing': 'mv_bushing__iexact',
                'MvCableLug': 'mv_cable_lug__iexact',
                'LvBushing': 'lv_bushing__iexact',
                'LvCableLug': 'lv_cable_lug__iexact',
                'OilLevel': 'oil_level__iexact',
                'InsulationLevel': 'insulation_level__iexact',
                'HornGap': 'horn_gap__iexact',
                'silica_gel ': 'silica_gel__iexact',
                'HasLinkage': 'has_linkage__iexact',
                'ArresterBodyGround': 'arrester_body_ground__iexact',
                'NeutralGround': 'neutral_ground__iexact',
                'StatusOfMounting': 'status_of_mounting__iexact',
                'MountingCondition': 'mounting_condition__iexact',
                'Owner': 'owner__icontains',
                'NLoadCurrent': 'N_load_current__icontains',
                'RSVoltage': 'R_S_Voltage__icontains',
                'RTVoltage': 'R_T_Voltage__icontains',
                'TSVoltage': 'T_S_Voltage__icontains',
                'station_code': 'transformer_data__basestation__station_code__iexact',
                'region': 'transformer_data__basestation__region__icontains',
                'csc': 'transformer_data__basestation__csc__icontains',
                'created_by': 'created_by__username__icontains',
                'updated_by': 'updated_by__username__icontains',
            }
        },
        'LatestLvFeederInspection': {
            'model': LvFeeder,
            'serializer': LvFeederSerializer2,
            'queryset': lambda: LvFeeder.objects.filter(
                inspection_data_id__in=Subquery(
                    Inspection.objects.filter(
                        transformer_data=OuterRef('inspection_data__transformer_data')
                    ).order_by('-created_at').values('id')[:1]
                )
            ),
            'fields': {
                'id': 'id__exact',
                'distribution_box_name': 'distribution_box_name__iexact',
                'station_code': 'inspection_data__transformer_data__basestation__station_code__iexact',
                'TransformerID': 'inspection_data__transformer_data__id__exact',
                'region': 'inspection_data__transformer_data__basestation__region__icontains',
                'csc': 'inspection_data__transformer_data__basestation__csc__icontains',
                'created_by': 'created_by__username__icontains',
                'updated_by': 'updated_by__username__icontains',
            }
        }

    }

    def get(self, request, *args, **kwargs):
        search_type = request.query_params.get('searchType')
        config = self.FILTER_CONFIG.get(search_type)

        if not config:
            return self.default_response(request)

        base_queryset = config.get('queryset', lambda: config['model'].objects.all())()
        filter_conditions = Q()

        created_date_range = request.query_params.getlist('created_date_range[]')
        if len(created_date_range) == 2:
            try:
                start_date = timezone.make_aware(datetime.strptime(created_date_range[0], '%Y-%m-%d'))
                end_date = timezone.make_aware(datetime.strptime(created_date_range[1], '%Y-%m-%d'))

                # Add one day to include full end date
                end_date = end_date.replace(hour=23, minute=59, second=59)

                # Apply filter only if model has 'created_at' or similar field
                if hasattr(config['model'], 'created_at'):
                    filter_conditions &= Q(created_at__range=(start_date, end_date))
                else:
                    # Optional: Log warning if model doesn't support this field
                    pass
            except ValueError as ve:
                return Response({"error": "Invalid date format in created_date_range"}, status=400)

        # Process updated_at filter for BaseStation and Transformer
        if search_type in ['BaseStation', 'Transformer'] and 'updated_at' in request.query_params:
            try:
                # Get the updated_at value from query params
                updated_at_value = request.query_params.get('updated_at')

                if updated_at_value:
                    # from django.utils import timezone
                    # from datetime import datetime
                    import pytz

                    # Parse the date string to a datetime object
                    try:
                        # Handle ISO format with Z (UTC)
                        if 'Z' in updated_at_value:
                            # Replace Z with +00:00 for ISO format compatibility
                            date_str = updated_at_value.replace('Z', '+00:00')
                            date_obj = datetime.fromisoformat(date_str)
                        else:
                            # Try simple date format
                            date_obj = datetime.strptime(updated_at_value, '%Y-%m-%d')
                            # Make timezone-aware
                            date_obj = pytz.UTC.localize(date_obj)
                    except Exception as parse_error:
                        print(f"Date parsing error: {parse_error}")
                        # Default to today if parsing fails
                        date_obj = timezone.now()

                    # Apply direct filter based on model
                    if search_type == 'BaseStation':
                        base_queryset = base_queryset.filter(updated_at__gt=date_obj)
                        print(f"Filtering BaseStation with updated_at > {date_obj}")
                    elif search_type == 'Transformer':
                        base_queryset = base_queryset.filter(updated_at__gt=date_obj)
                        print(f"Filtering Transformer with updated_at > {date_obj}")

                    print(f"Query: {base_queryset.query}")
            except Exception as e:
                print(f"Error processing updated_at filter: {e}")
                import traceback
                traceback.print_exc()

        if search_type == 'Transformer' and request.query_params.get('without_base_station', '').lower() == 'true':
            base_queryset = base_queryset.filter(basestation__isnull=True)
            print("Filtering TransformerData without a connected Basestation")


        if search_type == 'BaseStation' and request.query_params.get('without_transformer', '').lower() == 'true':
            base_queryset = base_queryset.filter(transformer_data_basestation__isnull=True)
            print("Filtering BaseStation without a connected TransformerData")

        # Handle regular filters
        for param_name, lookup in config['fields'].items():
            value = request.query_params.get(param_name)
            if value:
                if param_name == 'station_code' and value.lower() == 'null':
                    if search_type == 'BaseStation':
                        filter_conditions &= Q(station_code__isnull=True)
                    else:
                        filter_conditions &= Q(basestation__isnull=True)
                else:
                    filter_conditions &= Q(**{lookup: value})

        # Handle inspection status and date range filtering
        # inspection_status = request.query_params.get('inspection_status')
        # inspection_date_range = request.query_params.getlist('inspection_date_range[]')

        # if inspection_status and len(inspection_date_range) == 2:
        #     start_date, end_date = inspection_date_range

        #     if search_type == 'Transformer':
        #         inspected_transformers = Inspection.objects.filter(
        #             created_at__range=[start_date, end_date]
        #         ).values_list('transformer_data_id', flat=True).distinct()

        #         if inspection_status == 'not_inspected':
        #             filter_conditions &= ~Q(id__in=inspected_transformers)
        #         elif inspection_status == 'inspected':
        #             filter_conditions &= Q(id__in=inspected_transformers)

        inspection_status = request.query_params.get('inspection_status')
        inspection_date_range = request.query_params.getlist('inspection_date_range[]')

        # Set default 4-month range if inspection_date_range is not provided or incomplete
        if len(inspection_date_range) != 2:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=120)  # 4 months = ~120 days
            inspection_date_range = [start_date, end_date]
            use_default_range = True
        else:
            # Parse provided dates
            try:
                start_date = timezone.make_aware(datetime.strptime(inspection_date_range[0], '%Y-%m-%d'))
                end_date = timezone.make_aware(datetime.strptime(inspection_date_range[1], '%Y-%m-%d'))
                # Add one day to include full end date
                end_date = end_date.replace(hour=23, minute=59, second=59)
                use_default_range = False
            except ValueError:
                # If date parsing fails, use default range
                end_date = timezone.now()
                start_date = end_date - timedelta(days=120)
                inspection_date_range = [start_date, end_date]
                use_default_range = True

        if inspection_status:
            if search_type == 'Transformer':
                inspected_transformers = Inspection.objects.filter(
                    created_at__range=[start_date, end_date]
                ).values_list('transformer_data_id', flat=True).distinct()

                if inspection_status == 'not_inspected':
                    filter_conditions &= ~Q(id__in=inspected_transformers)
                elif inspection_status == 'inspected':
                    filter_conditions &= Q(id__in=inspected_transformers)

        # Apply filtering
        queryset = base_queryset.filter(filter_conditions)


        if search_type == 'BaseStation':
            gps_location = request.query_params.get('gps_location')
            if gps_location:
                try:
                    lat_str, lon_str = gps_location.split(',')
                    lat = float(lat_str.strip())
                    lon = float(lon_str.strip())
                except Exception:
                    # If GPS format is invalid, just return unsorted queryset
                    pass
                else:
                    def haversine(lat1, lon1, lat2, lon2):
                        from math import radians, cos, sin, asin, sqrt
                        R = 6371  # Earth radius in kilometers
                        dlat = radians(lat2 - lat1)
                        dlon = radians(lon2 - lon1)
                        a = sin(dlat/2)**2 + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon/2)**2
                        c = 2 * asin(sqrt(a))
                        return R * c

                    # Annotate each basestation with distance in meters
                    basestations_with_distance = []
                    for bs in queryset:
                        try:
                            if bs.gps_location and ',' in bs.gps_location:
                                bs_lat_str, bs_lon_str = bs.gps_location.split(',')
                                bs_lat = float(bs_lat_str.strip())
                                bs_lon = float(bs_lon_str.strip())
                                distance_km = haversine(lat, lon, bs_lat, bs_lon)
                                distance_m = round(distance_km * 1000, 1)
                                basestations_with_distance.append((distance_m, bs))
                        except Exception:
                            continue

                    # Sort by distance
                    basestations_with_distance.sort(key=lambda x: x[0])
                    # Replace queryset with sorted basestations
                    queryset = [bs for _, bs in basestations_with_distance]

                    # print(f"Filtered queryset: {queryset}")



        # Handle sorting for LatestInspection
        if search_type == 'LatestInspection':
            try:
                # Get voltage filters from request
                voltage_status = request.query_params.get('average_voltage', '').lower()
                unbalance_status = request.query_params.get('voltage_phase_unbalance', '').lower()
                total_transformer_load_status = request.query_params.get('total_transformer_load', '').lower()

                # Apply voltage range filters using Q objects
                if voltage_status:
                    if 'under' in voltage_status:
                        queryset = queryset.filter(Q(average_voltage__lt=360) | Q(average_voltage__isnull=True))
                    elif 'normal' in voltage_status:
                        queryset = queryset.filter(Q(average_voltage__gte=360) & Q(average_voltage__lte=440))
                    elif 'over' in voltage_status:
                        queryset = queryset.filter(average_voltage__gt=440)



                # Apply voltage unbalance filters using Q objects
                if unbalance_status:
                    if 'unbalanced' in unbalance_status:
                        queryset = queryset.filter(voltage_phase_unbalance__gt=3)
                    elif 'balanced' in unbalance_status:
                        queryset = queryset.filter(Q(voltage_phase_unbalance__lte=3) | Q(voltage_phase_unbalance__isnull=True))

                # Apply transformer load filters using Q objects
                if total_transformer_load_status:
                    if 'below20' in total_transformer_load_status:
                        queryset = queryset.filter(total_transformer_load__lte=20)
                    elif 'load20_50' in total_transformer_load_status:
                        queryset = queryset.filter(total_transformer_load__gt=20, total_transformer_load__lte=50)
                    elif 'load50_80' in total_transformer_load_status:
                        queryset = queryset.filter(total_transformer_load__gt=50, total_transformer_load__lte=80)
                    elif 'load80_100' in total_transformer_load_status:
                        queryset = queryset.filter(total_transformer_load__gt=80, total_transformer_load__lte=100)
                    elif 'above100' in total_transformer_load_status:
                        queryset = queryset.filter(total_transformer_load__gt=100)

                # Apply final sorting
                queryset = queryset.order_by('-created_at')

            except Exception as e:
                # Log the error if needed
                print(f"Error in voltage filtering: {str(e)}")
                # Return unfiltered queryset
                queryset = queryset.order_by('-created_at')

        if search_type == 'LatestLvFeederInspection':
            try:
                # Get transformer load filters from request
                transformer_load_status = request.query_params.get('transformer_load', '').lower()
                current_phase_unbalance_status = request.query_params.get('current_phase_unbalance', '').lower()
                percentage_of_neutral_status = request.query_params.get('percentage_of_neutral', '').lower()

                # Apply transformer load filters using Q objects
                if transformer_load_status:
                    if 'below20' in transformer_load_status:
                        queryset = queryset.filter(transformer_load__lte=20)
                    elif 'load20_50' in transformer_load_status:
                        queryset = queryset.filter(transformer_load__gt=20, transformer_load__lte=50)
                    elif 'load50_80' in transformer_load_status:
                        queryset = queryset.filter(transformer_load__gt=50, transformer_load__lte=80)
                    elif 'load80_100' in transformer_load_status:
                        queryset = queryset.filter(transformer_load__gt=80, transformer_load__lte=100)
                    elif 'above100' in transformer_load_status:
                        queryset = queryset.filter(transformer_load__gt=100)

                # Apply current phase unbalance filters using Q objects
                if current_phase_unbalance_status:
                    if 'unbalanced' in current_phase_unbalance_status:
                        queryset = queryset.filter(current_phase_unbalance__gt=10)
                    elif 'balanced' in current_phase_unbalance_status:
                        queryset = queryset.filter(Q(current_phase_unbalance__lte=10) | Q(current_phase_unbalance__isnull=True))

                if percentage_of_neutral_status:
                    if 'normal' in percentage_of_neutral_status:
                        queryset = queryset.filter(percentage_of_neutral__lte=20)
                    elif 'high' in percentage_of_neutral_status:
                        queryset = queryset.filter(percentage_of_neutral__gt=20)



            except Exception as e:
                # Log the error if needed
                print(f"Error in voltage filtering: {str(e)}")
                # Return unfiltered queryset
                queryset = queryset.order_by('-created_at')



        return self.paginate_and_serialize(request, queryset, config['serializer'])

    def default_response(self, request):
        """Handle default case when searchType is not recognized"""
        filter_conditions = Q()
        for param in ['region', 'csc']:
            value = request.query_params.get(param)
            if value:
                filter_conditions &= Q(**{f"{param}__icontains": value})

        queryset = Basestation.objects.filter(filter_conditions)
        return self.paginate_and_serialize(request, queryset, BasestationSerializer)

    def paginate_and_serialize(self, request, queryset, serializer_class):
        """Handle pagination and serialization with batch processing for large datasets"""
        paginator = CustomPageNumberPagination()
        page = request.query_params.get('page')
        page_size = request.query_params.get('pageSize')

        if page and page_size:
            try:
                page_size_int = int(page_size)

                # Check if this is a large request that needs batch processing
                if page_size_int > 500:
                    return self._handle_large_request(request, queryset, serializer_class, page_size_int)

                # Normal pagination for smaller requests
                paginated_queryset = paginator.paginate_queryset(queryset, request)
                serializer = serializer_class(paginated_queryset, many=True)
                return paginator.get_paginated_response(serializer.data)

            except ValueError:
                # If page_size is not a valid integer, fall back to normal pagination
                paginated_queryset = paginator.paginate_queryset(queryset, request)
                serializer = serializer_class(paginated_queryset, many=True)
                return paginator.get_paginated_response(serializer.data)

        serializer = serializer_class(queryset, many=True)
        return Response(serializer.data, status=200)

    def _handle_large_request(self, request, queryset, serializer_class, requested_page_size):
        """Handle large requests with batch processing and caching"""
        # Create a unique cache key based on user and request parameters
        user_id = getattr(request.user, 'id', 'anonymous')
        cache_key_params = {
            'searchType': request.query_params.get('searchType', ''),
            'region': request.query_params.get('region', ''),
            'csc': request.query_params.get('csc', ''),
            'page': request.query_params.get('page', '1'),
            'pageSize': str(requested_page_size)
        }
        cache_key_str = '&'.join([f"{k}={v}" for k, v in cache_key_params.items()])
        user_key = f"filter_lock_{user_id}_{hash(cache_key_str)}"

        # Check if a similar request is already in progress
        if safe_cache_get(user_key):
            return Response(
                {"error": "A filter request is already in progress for your account. Please wait."},
                status=429
            )

        # Set cache lock for 5 minutes
        safe_cache_set(user_key, True, timeout=300)

        # Log the large request for monitoring
        search_type = request.query_params.get('searchType', 'Unknown')
        print(f"Processing large request: {search_type}, pageSize: {requested_page_size}, user: {user_id}")

        try:
            # Process in batches of 500
            batch_size = 500
            page = int(request.query_params.get('page', 1))

            # Optimize queryset with select_related/prefetch_related for better performance
            search_type = request.query_params.get('searchType', '')
            if search_type == 'Transformer':
                # For transformers, prefetch related basestation data
                queryset = queryset.select_related('basestation', 'created_by', 'updated_by')
            elif search_type == 'BaseStation':
                # For basestations, prefetch related user data
                queryset = queryset.select_related('created_by', 'updated_by')

            # Calculate the total count for pagination info (use efficient count)
            total_count = queryset.count()

            # Calculate start and end indices for the requested page
            start_index = (page - 1) * requested_page_size
            end_index = start_index + requested_page_size

            # Get the slice of data we need with iterator for memory efficiency
            queryset_slice = queryset[start_index:end_index]

            # Process the slice in batches to avoid memory issues
            all_data = []
            current_batch = []

            # Convert queryset to list in chunks to avoid holding large querysets in memory
            for i, item in enumerate(queryset_slice.iterator(chunk_size=batch_size)):
                current_batch.append(item)

                # Process batch when it reaches batch_size or we're at the end
                if len(current_batch) >= batch_size:
                    serializer = serializer_class(current_batch, many=True)
                    all_data.extend(serializer.data)
                    current_batch = []

            # Process any remaining items in the last batch
            if current_batch:
                serializer = serializer_class(current_batch, many=True)
                all_data.extend(serializer.data)

            # Create pagination response with additional metadata
            has_next = end_index < total_count
            has_previous = page > 1

            # Build next/previous URLs with all current query parameters
            current_params = dict(request.query_params)

            next_url = None
            if has_next:
                next_params = current_params.copy()
                next_params['page'] = str(page + 1)
                next_url = '?' + '&'.join([f"{k}={v}" for k, v in next_params.items()])

            previous_url = None
            if has_previous:
                prev_params = current_params.copy()
                prev_params['page'] = str(page - 1)
                previous_url = '?' + '&'.join([f"{k}={v}" for k, v in prev_params.items()])

            response_data = {
                'count': total_count,
                'next': next_url,
                'previous': previous_url,
                'results': all_data,
                'batch_processed': True,  # Indicate this was batch processed
                'batch_size': batch_size,
                'page_size': requested_page_size,
                'processing_time': f"Processed {len(all_data)} items in batches"
            }

            print(f"Successfully processed large request: {len(all_data)} items returned")
            return Response(response_data, status=200)

        except Exception as e:
            # Log detailed error information
            import traceback
            error_details = traceback.format_exc()

            if request.user.is_authenticated:
                log_error(
                    level='ERROR',
                    message=f'BasestationsFilteredAPIView._handle_large_request() failed: {type(e).__name__}: {str(e)}',
                    user=request.user,
                    traceback=error_details,
                    ip_address=request.META.get('REMOTE_ADDR')
                )

            return Response(
                {
                    "error": "Failed to process large request",
                    "details": str(e),
                    "message": "The request was too large to process. Please try with a smaller pageSize or contact support."
                },
                status=500
            )
        finally:
            # Always clear the cache lock
            safe_cache_delete(user_key)

class AsyncBasestationsFilteredAPIView(AsyncAPIView):
    """
    Async version of BasestationsFilteredAPIView optimized for high concurrency.
    Handles multiple simultaneous requests without connection pool exhaustion.
    """

    # Disable authentication for testing - enable in production
    permission_classes = []
    authentication_classes = []

    FILTER_CONFIG = {
        'BaseStation': {
            'model': Basestation,
            'serializer': BasestationSerializer,
            'fields': {
                'station_code': 'station_code__iexact',
                'substation': 'substation__icontains',
                'feeder': 'feeder__icontains',
                'address': 'address__icontains',
                'gps_location': 'gps_location__icontains',
                'region': 'region__icontains',
                'csc': 'csc__icontains',
                "station_type": 'station_type__iexact',
                'created_by': 'created_by__username__icontains',
                'updated_by': 'updated_by__username__icontains',
            }
        },
        'Transformer': {
            'model': TransformerData,
            'serializer': TransformerDataSerializer2,
            'fields': {
                'id': 'id__exact',
                'trafo_type': 'trafo_type__iexact',
                'capacity': 'capacity__iexact',
                'primary_voltage': 'primary_voltage__exact',
                'colling_type': 'colling_type__iexact',
                'manufacturer': 'manufacturer__iexact',
                'vector_group': 'vector_group__iexact',
                'year_of_manufacturing': 'year_of_manufacturing__range',
                'dt_number': 'dt_number__icontains',
                'serial_number': 'serial_number__icontains',
                'status': 'status__iexact',
                'service_type': 'service_type__iexact',
                'impedance_voltage': 'impedance_voltage__iexact',
                'winding_weight': 'winding_weight__iexact',
                'oil_weight': 'oil_weight__iexact',
                'station_code': 'basestation__station_code__iexact',
                'region': 'basestation__region__icontains',
                'csc': 'basestation__csc__icontains',
                'created_by': 'created_by__username__icontains',
                'updated_by': 'updated_by__username__icontains',
            }
        },
        'Inspection': {
            'model': Inspection,
            'serializer': InspectionSerializer2,
            'fields': {
                'id': 'id__exact',
                'ServiceType': 'service_type__iexact',
                'BodyCondition': 'body_condition__iexact',
                'Arrester': 'arrester__iexact',
                'DropOutFuse': 'drop_out__iexact',
                'FuseLink': 'fuse_link__iexact',
                'MVBushing': 'mv_bushing__iexact',
                'MvCableLug': 'mv_cable_lug__iexact',
                'LVBushing': 'lv_bushing__iexact',
                'LvCableLug': 'lv_cable_lug__iexact',
                'OilLevel': 'oil_level__iexact',
                'InsulationLevel': 'insulation_level__iexact',
                'HornGap': 'horn_gap__iexact',
                'silica_gel ': 'silica_gel__iexact',
                'HasLinkage': 'has_linkage__iexact',
                'ArresterBodyGround': 'arrester_body_ground__iexact',
                'NeutralGround': 'neutral_ground__iexact',
                'StatusOfMounting': 'status_of_mounting__iexact',
                'MountingCondition': 'mounting_condition__iexact',
                'Owner': 'owner__icontains',
                'NLoadCurrent': 'N_load_current__icontains',
                'RSVoltage': 'R_S_Voltage__icontains',
                'RTVoltage': 'R_T_Voltage__icontains',
                'TSVoltage': 'T_S_Voltage__icontains',
                'station_code': 'transformer_data__basestation__station_code__iexact',
                'region': 'transformer_data__basestation__region__icontains',
                'csc': 'transformer_data__basestation__csc__icontains',
                'created_by': 'created_by__username__icontains',
                'updated_by': 'updated_by__username__icontains',
            }
        },
        'LatestInspection': {
            'model': Inspection,
            'serializer': InspectionSerializer2,
            'queryset': lambda: Inspection.objects.filter(
                id__in=Subquery(
                    Inspection.objects.filter(
                        transformer_data=OuterRef('transformer_data')
                    ).order_by('-created_at').values('id')[:1]
                )
            ),
            'fields': {
                'id': 'id__exact',
                'ServiceType': 'service_type__iexact',
                'BodyCondition': 'body_condition__iexact',
                'Arrester': 'arrester__iexact',
                'DropOutFuse': 'drop_out__iexact',
                'FuseLink': 'fuse_link__iexact',
                'MvBushing': 'mv_bushing__iexact',
                'MvCableLug': 'mv_cable_lug__iexact',
                'LvBushing': 'lv_bushing__iexact',
                'LvCableLug': 'lv_cable_lug__iexact',
                'OilLevel': 'oil_level__iexact',
                'InsulationLevel': 'insulation_level__iexact',
                'HornGap': 'horn_gap__iexact',
                'silica_gel ': 'silica_gel__iexact',
                'HasLinkage': 'has_linkage__iexact',
                'ArresterBodyGround': 'arrester_body_ground__iexact',
                'NeutralGround': 'neutral_ground__iexact',
                'StatusOfMounting': 'status_of_mounting__iexact',
                'MountingCondition': 'mounting_condition__iexact',
                'Owner': 'owner__icontains',
                'NLoadCurrent': 'N_load_current__icontains',
                'RSVoltage': 'R_S_Voltage__icontains',
                'RTVoltage': 'R_T_Voltage__icontains',
                'TSVoltage': 'T_S_Voltage__icontains',
                'station_code': 'transformer_data__basestation__station_code__iexact',
                'region': 'transformer_data__basestation__region__icontains',
                'csc': 'transformer_data__basestation__csc__icontains',
                'created_by': 'created_by__username__icontains',
                'updated_by': 'updated_by__username__icontains',
            }
        },
        'LatestLvFeederInspection': {
            'model': LvFeeder,
            'serializer': LvFeederSerializer2,
            'queryset': lambda: LvFeeder.objects.filter(
                inspection_data_id__in=Subquery(
                    Inspection.objects.filter(
                        transformer_data=OuterRef('inspection_data__transformer_data')
                    ).order_by('-created_at').values('id')[:1]
                )
            ),
            'fields': {
                'id': 'id__exact',
                'distribution_box_name': 'distribution_box_name__iexact',
                'station_code': 'inspection_data__transformer_data__basestation__station_code__iexact',
                'TransformerID': 'inspection_data__transformer_data__id__exact',
                'region': 'inspection_data__transformer_data__basestation__region__icontains',
                'csc': 'inspection_data__transformer_data__basestation__csc__icontains',
                'created_by': 'created_by__username__icontains',
                'updated_by': 'updated_by__username__icontains',
            }
        }
    }

    async def get(self, request, *args, **kwargs):
        """Async version of the get method with improved connection handling"""
        try:
            search_type = request.query_params.get('searchType', 'BaseStation')
            config = self.FILTER_CONFIG.get(search_type)

            if not config:
                return await self.async_default_response(request)

            # Use database_sync_to_async for database operations
            base_queryset = await database_sync_to_async(
                lambda: config.get('queryset', lambda: config['model'].objects.all())()
            )()

            filter_conditions = Q()

            # Handle date range filtering
            created_date_range = request.query_params.getlist('created_date_range[]')
            if len(created_date_range) == 2:
                try:
                    start_date = timezone.make_aware(datetime.strptime(created_date_range[0], '%Y-%m-%d'))
                    end_date = timezone.make_aware(datetime.strptime(created_date_range[1], '%Y-%m-%d'))
                    end_date = end_date.replace(hour=23, minute=59, second=59)

                    if hasattr(config['model'], 'created_at'):
                        filter_conditions &= Q(created_at__range=(start_date, end_date))
                except ValueError:
                    return Response({"error": "Invalid date format in created_date_range"}, status=400)

            # Handle regular filters asynchronously
            for param_name, lookup in config['fields'].items():
                value = request.query_params.get(param_name)
                if value:
                    if param_name == 'station_code' and value.lower() == 'null':
                        if search_type == 'BaseStation':
                            filter_conditions &= Q(station_code__isnull=True)
                        else:
                            filter_conditions &= Q(basestation__isnull=True)
                    else:
                        filter_conditions &= Q(**{lookup: value})

            # Handle special filters for transformers without base stations
            if search_type == 'Transformer' and request.query_params.get('without_base_station', '').lower() == 'true':
                base_queryset = await database_sync_to_async(
                    lambda: base_queryset.filter(basestation__isnull=True)
                )()

            # Handle special filters for base stations without transformers
            if search_type == 'BaseStation' and request.query_params.get('without_transformer', '').lower() == 'true':
                base_queryset = await database_sync_to_async(
                    lambda: base_queryset.filter(transformer_data_basestation__isnull=True)
                )()

            # Apply filter conditions
            if filter_conditions:
                base_queryset = await database_sync_to_async(
                    lambda: base_queryset.filter(filter_conditions)
                )()

            return await self.async_paginate_and_serialize(request, base_queryset, config['serializer'])

        except Exception as e:
            # Async error logging (only if user exists and is authenticated)
            try:
                if hasattr(request, 'user') and request.user and request.user.is_authenticated:
                    await database_sync_to_async(log_error)(
                        level='ERROR',
                        message=f'AsyncBasestationsFilteredAPIView.get() failed: {type(e).__name__}: {str(e)}',
                        user=request.user,
                        traceback=str(e),
                        ip_address=request.META.get('REMOTE_ADDR')
                    )
            except Exception:
                # If logging fails, continue without logging
                pass

            return Response(
                {"error": "Failed to process request", "details": str(e)},
                status=500
            )
    async def async_default_response(self, request):
        """Async version of default_response method"""
        filter_conditions = Q()
        for param in ['region', 'csc']:
            value = request.query_params.get(param)
            if value:
                filter_conditions &= Q(**{f"{param}__icontains": value})

        queryset = await database_sync_to_async(
            lambda: Basestation.objects.filter(filter_conditions)
        )()
        return await self.async_paginate_and_serialize(request, queryset, BasestationSerializer)

    async def async_paginate_and_serialize(self, request, queryset, serializer_class):
        """Async version of paginate_and_serialize with improved connection handling"""
        try:
            paginator = CustomPageNumberPagination()
            page = request.query_params.get('page')
            page_size = request.query_params.get('pageSize')

            if page and page_size:
                try:
                    page_size_int = int(page_size)

                    # Check if this is a large request that needs batch processing
                    if page_size_int > 500:
                        return await self._async_handle_large_request(request, queryset, serializer_class, page_size_int)

                    # Normal pagination for smaller requests
                    paginated_queryset = await database_sync_to_async(
                        lambda: paginator.paginate_queryset(queryset, request)
                    )()

                    # Convert queryset to list to avoid connection issues
                    queryset_list = await database_sync_to_async(list)(paginated_queryset)

                    # Serialize the data
                    serializer = await database_sync_to_async(
                        lambda: serializer_class(queryset_list, many=True)
                    )()

                    serialized_data = await database_sync_to_async(
                        lambda: serializer.data
                    )()

                    return await database_sync_to_async(
                        lambda: paginator.get_paginated_response(serialized_data)
                    )()

                except ValueError:
                    # If page_size is not a valid integer, fall back to normal pagination
                    paginated_queryset = await database_sync_to_async(
                        lambda: paginator.paginate_queryset(queryset, request)
                    )()

                    queryset_list = await database_sync_to_async(list)(paginated_queryset)
                    serializer = await database_sync_to_async(
                        lambda: serializer_class(queryset_list, many=True)
                    )()
                    serialized_data = await database_sync_to_async(lambda: serializer.data)()

                    return await database_sync_to_async(
                        lambda: paginator.get_paginated_response(serialized_data)
                    )()

            # No pagination - serialize all data
            queryset_list = await database_sync_to_async(list)(queryset)
            serializer = await database_sync_to_async(
                lambda: serializer_class(queryset_list, many=True)
            )()
            serialized_data = await database_sync_to_async(lambda: serializer.data)()

            return Response(serialized_data, status=200)

        except Exception as e:
            return Response(
                {"error": "Failed to paginate and serialize data", "details": str(e)},
                status=500
            )
    async def _async_handle_large_request(self, request, queryset, serializer_class, requested_page_size):
        """Async version of large request handler with improved connection management"""
        try:
            # Create a unique cache key based on user and request parameters
            user_id = getattr(request.user, 'id', 'anonymous')
            cache_key_params = {
                'searchType': request.query_params.get('searchType', ''),
                'region': request.query_params.get('region', ''),
                'csc': request.query_params.get('csc', ''),
                'page': request.query_params.get('page', '1'),
                'pageSize': str(requested_page_size)
            }
            cache_key_str = '&'.join([f"{k}={v}" for k, v in cache_key_params.items()])
            user_key = f"async_filter_lock_{user_id}_{hash(cache_key_str)}"

            # Check if a similar request is already in progress
            if await database_sync_to_async(safe_cache_get)(user_key):
                return Response(
                    {"error": "A filter request is already in progress for your account. Please wait."},
                    status=429
                )

            # Set cache lock for 5 minutes
            await database_sync_to_async(safe_cache_set)(user_key, True, timeout=300)

            try:
                # Process in batches of 500
                batch_size = 500
                page = int(request.query_params.get('page', 1))

                # Optimize queryset with select_related/prefetch_related for better performance
                search_type = request.query_params.get('searchType', '')
                if search_type == 'Transformer':
                    # For transformers, prefetch related basestation data
                    queryset = await database_sync_to_async(
                        lambda: queryset.select_related('basestation', 'created_by', 'updated_by')
                    )()
                elif search_type == 'BaseStation':
                    # For basestations, prefetch related user data
                    queryset = await database_sync_to_async(
                        lambda: queryset.select_related('created_by', 'updated_by')
                    )()

                # Calculate the total count for pagination info (use efficient count)
                total_count = await database_sync_to_async(queryset.count)()

                # Calculate start and end indices for the requested page
                start_index = (page - 1) * requested_page_size
                end_index = start_index + requested_page_size

                # Get the slice of data we need
                queryset_slice = await database_sync_to_async(
                    lambda: queryset[start_index:end_index]
                )()

                # Process the slice in batches to avoid memory issues
                all_data = []
                current_batch = []

                # Convert queryset to list in chunks to avoid holding large querysets in memory
                # Process in batches to avoid memory issues
                queryset_list = await database_sync_to_async(list)(queryset_slice)

                for i in range(0, len(queryset_list), batch_size):
                    batch = queryset_list[i:i + batch_size]

                    # Serialize batch
                    serializer = await database_sync_to_async(
                        lambda: serializer_class(batch, many=True)
                    )()
                    serialized_data = await database_sync_to_async(lambda: serializer.data)()
                    all_data.extend(serialized_data)

                    # Allow other coroutines to run
                    await asyncio.sleep(0)

                # Create pagination response
                has_next = end_index < total_count
                has_previous = page > 1

                # Build next/previous URLs with all current query parameters
                current_params = dict(request.query_params)

                next_url = None
                if has_next:
                    next_params = current_params.copy()
                    next_params['page'] = str(page + 1)
                    next_url = '?' + '&'.join([f"{k}={v}" for k, v in next_params.items()])

                previous_url = None
                if has_previous:
                    prev_params = current_params.copy()
                    prev_params['page'] = str(page - 1)
                    previous_url = '?' + '&'.join([f"{k}={v}" for k, v in prev_params.items()])

                response_data = {
                    'count': total_count,
                    'next': next_url,
                    'previous': previous_url,
                    'results': all_data,
                    'async_processed': True,  # Indicate this was async processed
                    'batch_size': batch_size,
                    'page_size': requested_page_size,
                    'processing_time': f"Async processed {len(all_data)} items in batches"
                }

                return Response(response_data, status=200)

            except Exception as e:
                # Log detailed error information
                import traceback
                error_details = traceback.format_exc()

                if request.user.is_authenticated:
                    await database_sync_to_async(log_error)(
                        level='ERROR',
                        message=f'AsyncBasestationsFilteredAPIView._async_handle_large_request() failed: {type(e).__name__}: {str(e)}',
                        user=request.user,
                        traceback=error_details,
                        ip_address=request.META.get('REMOTE_ADDR')
                    )

                return Response(
                    {
                        "error": "Failed to process large async request",
                        "details": str(e),
                        "message": "The request was too large to process. Please try with a smaller pageSize or contact support."
                    },
                    status=500
                )
            finally:
                # Always clear the cache lock
                await database_sync_to_async(safe_cache_delete)(user_key)

        except Exception as e:
            return Response(
                {"error": "Failed to handle large request", "details": str(e)},
                status=500
            )





from django.http import JsonResponse
from django.db.models import Count, F, Max, Q, Case, When, Value, CharField
from .models import TransformerData, Inspection, LvFeeder


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_statistics(request):
    """
    Comprehensive dashboard statistics API endpoint.
    Returns aggregated data for basestations, transformers, inspections, and LV feeders.

    Query Parameters:
    - region: Optional filter by region (e.g., ?region=North Region)

    Example URLs:
    - /api/transformer/dashboard-statistics/ (all data)
    - /api/transformer/dashboard-statistics/?region=North Region (filtered by region)
    """
    try:
        # Get region filter from query parameters
        region_filter = request.GET.get('region', None)

        # Base querysets
        basestation_qs = Basestation.objects.all()
        transformer_qs = TransformerData.objects.all()
        inspection_qs = Inspection.objects.all()
        lv_feeder_qs = LvFeeder.objects.all()

        # Apply region filter if provided
        if region_filter:
            basestation_qs = basestation_qs.filter(region=region_filter)
            transformer_qs = transformer_qs.filter(basestation__region=region_filter)
            inspection_qs = inspection_qs.filter(transformer_data__basestation__region=region_filter)
            lv_feeder_qs = lv_feeder_qs.filter(inspection_data__transformer_data__basestation__region=region_filter)

        # BASESTATION STATISTICS
        basestation_stats = {
            'total': basestation_qs.count(),
            'byType': dict(basestation_qs.values('station_type').annotate(count=Count('station_code')).values_list('station_type', 'count')),
            'byRegion': dict(basestation_qs.values('region').annotate(count=Count('station_code')).values_list('region', 'count')),
            'byCSC': dict(basestation_qs.values('csc').annotate(count=Count('station_code')).values_list('csc', 'count')),
            'bySubstation': dict(basestation_qs.values('substation').annotate(count=Count('station_code')).values_list('substation', 'count')),
            'byFeeder': dict(basestation_qs.values('feeder').annotate(count=Count('station_code')).values_list('feeder', 'count'))
        }

        # TRANSFORMER STATISTICS
        transformer_stats = {
            'total': transformer_qs.count(),
            'byType': dict(transformer_qs.values('trafo_type').annotate(count=Count('id')).values_list('trafo_type', 'count')),
            'byCapacity': dict(transformer_qs.values('capacity').annotate(count=Count('id')).values_list('capacity', 'count')),
            'byPrimaryVoltage': dict(transformer_qs.values('primary_voltage').annotate(count=Count('id')).values_list('primary_voltage', 'count')),
            'byStatus': dict(transformer_qs.values('status').annotate(count=Count('id')).values_list('status', 'count')),
            'byManufacturer': dict(transformer_qs.values('manufacturer').annotate(count=Count('id')).values_list('manufacturer', 'count')),
            'byServiceType': dict(transformer_qs.values('service_type').annotate(count=Count('id')).values_list('service_type', 'count')),
            'byCoolingType': dict(transformer_qs.values('colling_type').annotate(count=Count('id')).values_list('colling_type', 'count')),
            'byVectorGroup': dict(transformer_qs.values('vector_group').annotate(count=Count('id')).values_list('vector_group', 'count')),
        }

        # Add year of manufacturing ranges for transformers
        current_year = timezone.now().year
        transformer_year_ranges = {
            f'{current_year-4}-{current_year}': transformer_qs.filter(year_of_manufacturing__gte=current_year-4).count(),
            f'{current_year-9}-{current_year-5}': transformer_qs.filter(year_of_manufacturing__gte=current_year-9, year_of_manufacturing__lt=current_year-4).count(),
            f'{current_year-14}-{current_year-10}': transformer_qs.filter(year_of_manufacturing__gte=current_year-14, year_of_manufacturing__lt=current_year-9).count(),
            f'{current_year-19}-{current_year-15}': transformer_qs.filter(year_of_manufacturing__gte=current_year-19, year_of_manufacturing__lt=current_year-14).count(),
            f'Before {current_year-20}': transformer_qs.filter(year_of_manufacturing__lt=current_year-19).count(),
        }
        transformer_stats['byYearOfManufacturing'] = transformer_year_ranges

        # INSPECTION STATISTICS
        # Get latest inspections only
        latest_inspection_ids = inspection_qs.values('transformer_data').annotate(
            latest_id=Max('id')
        ).values_list('latest_id', flat=True)

        latest_inspections = inspection_qs.filter(id__in=latest_inspection_ids)

        inspection_stats = {
            'total': latest_inspections.count(),
            'byBodyCondition': dict(latest_inspections.values('body_condition').annotate(count=Count('id')).values_list('body_condition', 'count')),
            'byArrester': dict(latest_inspections.values('arrester').annotate(count=Count('id')).values_list('arrester', 'count')),
            'byDropOut': dict(latest_inspections.values('drop_out').annotate(count=Count('id')).values_list('drop_out', 'count')),
            'byFuseLink': dict(latest_inspections.values('fuse_link').annotate(count=Count('id')).values_list('fuse_link', 'count')),
            'byMvBushing': dict(latest_inspections.values('mv_bushing').annotate(count=Count('id')).values_list('mv_bushing', 'count')),
            'byMvCableLug': dict(latest_inspections.values('mv_cable_lug').annotate(count=Count('id')).values_list('mv_cable_lug', 'count')),
            'byLvBushing': dict(latest_inspections.values('lv_bushing').annotate(count=Count('id')).values_list('lv_bushing', 'count')),
            'byLvCableLug': dict(latest_inspections.values('lv_cable_lug').annotate(count=Count('id')).values_list('lv_cable_lug', 'count')),
            'byOilLevel': dict(latest_inspections.values('oil_level').annotate(count=Count('id')).values_list('oil_level', 'count')),
            'byInsulationLevel': dict(latest_inspections.values('insulation_level').annotate(count=Count('id')).values_list('insulation_level', 'count')),
            'byHornGap': dict(latest_inspections.values('horn_gap').annotate(count=Count('id')).values_list('horn_gap', 'count')),
            'bySilicaGel': dict(latest_inspections.values('silica_gel').annotate(count=Count('id')).values_list('silica_gel', 'count')),
            'byHasLinkage': dict(latest_inspections.values('has_linkage').annotate(count=Count('id')).values_list('has_linkage', 'count')),
            'byArresterBodyGround': dict(latest_inspections.values('arrester_body_ground').annotate(count=Count('id')).values_list('arrester_body_ground', 'count')),
            'byNeutralGround': dict(latest_inspections.values('neutral_ground').annotate(count=Count('id')).values_list('neutral_ground', 'count')),
            'byStatusOfMounting': dict(latest_inspections.values('status_of_mounting').annotate(count=Count('id')).values_list('status_of_mounting', 'count')),
            'byMountingCondition': dict(latest_inspections.values('mounting_condition').annotate(count=Count('id')).values_list('mounting_condition', 'count')),
        }

        # Add voltage and load ranges for inspections
        voltage_ranges = {
            'Under Voltage (< 360V)': latest_inspections.filter(average_voltage__lt=360).count(),
            'Normal 360-440V': latest_inspections.filter(average_voltage__gte=360, average_voltage__lte=440).count(),
            'Over Voltage (> 440V)': latest_inspections.filter(average_voltage__gt=440).count(),
        }
        inspection_stats['byVoltageRanges'] = voltage_ranges

        # Note: Inspection model only has N_load_current field, not separate R/S/T
        load_current_ranges = {
            'Below 10A': latest_inspections.filter(N_load_current__lt=50).count(),
            '10-30A': latest_inspections.filter(N_load_current__gte=10, N_load_current__lt=30).count(),
            '30-50A': latest_inspections.filter(N_load_current__gte=30, N_load_current__lt=50).count(),
            '50-100A': latest_inspections.filter(N_load_current__gte=50, N_load_current__lt=100).count(),
            'Above 100A': latest_inspections.filter(N_load_current__gt=100).count(),
        }
        inspection_stats['byLoadCurrentRanges'] = load_current_ranges

        voltage_unbalance_ranges = {
            'Balanced (≤3%)': latest_inspections.filter(voltage_phase_unbalance__lte=3).count(),
            'Unbalanced  (>3%)': latest_inspections.filter(voltage_phase_unbalance__gt=3).count(),
        }
        inspection_stats['byVoltageUnbalanceRanges'] = voltage_unbalance_ranges

        transformer_load_ranges = {
            'Below 20%': latest_inspections.filter(total_transformer_load__lt=20).count(),
            '20-50%': latest_inspections.filter(total_transformer_load__gte=20, total_transformer_load__lt=50).count(),
            '50-80%': latest_inspections.filter(total_transformer_load__gte=50, total_transformer_load__lt=80).count(),
            '80-100%': latest_inspections.filter(total_transformer_load__gte=80, total_transformer_load__lt=100).count(),
            'Above 100%': latest_inspections.filter(total_transformer_load__gt=100).count(),
        }
        inspection_stats['byTransformerLoadRanges'] = transformer_load_ranges

        # LV FEEDER STATISTICS
        lv_feeder_stats = {
            'total': lv_feeder_qs.count(),
            'byRFuseRating': dict(lv_feeder_qs.values('R_fuse_rating').annotate(count=Count('id')).values_list('R_fuse_rating', 'count')),
            'bySFuseRating': dict(lv_feeder_qs.values('S_fuse_rating').annotate(count=Count('id')).values_list('S_fuse_rating', 'count')),
            'byTFuseRating': dict(lv_feeder_qs.values('T_fuse_rating').annotate(count=Count('id')).values_list('T_fuse_rating', 'count')),
        }

        # Add load current ranges for LV feeders (using R, S, T load current fields)
        lv_load_current_ranges = {
            '0-50A': lv_feeder_qs.filter(
                R_load_current__lte=50,
                S_load_current__lte=50,
                T_load_current__lte=50
            ).count(),
            '50-100A': lv_feeder_qs.filter(
                Q(R_load_current__gt=50, R_load_current__lte=100) |
                Q(S_load_current__gt=50, S_load_current__lte=100) |
                Q(T_load_current__gt=50, T_load_current__lte=100)
            ).count(),
            '100-150A': lv_feeder_qs.filter(
                Q(R_load_current__gt=100, R_load_current__lte=150) |
                Q(S_load_current__gt=100, S_load_current__lte=150) |
                Q(T_load_current__gt=100, T_load_current__lte=150)
            ).count(),
            '150-200A': lv_feeder_qs.filter(
                Q(R_load_current__gt=150, R_load_current__lte=200) |
                Q(S_load_current__gt=150, S_load_current__lte=200) |
                Q(T_load_current__gt=150, T_load_current__lte=200)
            ).count(),
            '200-250A': lv_feeder_qs.filter(
                Q(R_load_current__gt=200, R_load_current__lte=250) |
                Q(S_load_current__gt=200, S_load_current__lte=250) |
                Q(T_load_current__gt=200, T_load_current__lte=250)
            ).count(),
            'Above 250A': lv_feeder_qs.filter(
                Q(R_load_current__gt=250) |
                Q(S_load_current__gt=250) |
                Q(T_load_current__gt=250)
            ).count(),
        }
        lv_feeder_stats['byLoadCurrentRanges'] = lv_load_current_ranges

        # Add current unbalance ranges for LV feeders
        lv_current_unbalance_ranges = {
            'Balanced (≤10%)': lv_feeder_qs.filter(current_phase_unbalance__lte=10).count(),
            'Unbalanced (>10%)': lv_feeder_qs.filter(current_phase_unbalance__gt=10).count(),
        }
        lv_feeder_stats['byCurrentUnbalanceRanges'] = lv_current_unbalance_ranges

        # Add transformer load ranges for LV feeders
        # lv_transformer_load_ranges = {
        #     '0-25%': lv_feeder_qs.filter(transformer_load__gte=0, transformer_load__lte=25).count(),
        #     '25-50%': lv_feeder_qs.filter(transformer_load__gt=25, transformer_load__lte=50).count(),
        #     '50-75%': lv_feeder_qs.filter(transformer_load__gt=50, transformer_load__lte=75).count(),
        #     '75-100%': lv_feeder_qs.filter(transformer_load__gt=75, transformer_load__lte=100).count(),
        #     'Above 100%': lv_feeder_qs.filter(transformer_load__gt=100).count(),
        # }
        # lv_feeder_stats['byTransformerLoadRanges'] = lv_transformer_load_ranges

        # Add neutral load ranges for LV feeders
        lv_neutral_load_ranges = {
            'Normal (≤20%)': lv_feeder_qs.filter(percentage_of_neutral__lte=20).count(),
            'High (>20%)': lv_feeder_qs.filter(percentage_of_neutral__gt=20).count(),
        }
        lv_feeder_stats['byNeutralLoadRanges'] = lv_neutral_load_ranges

        # Prepare response data
        response_data = {
            'basestations': basestation_stats,
            'transformers': transformer_stats,
            'inspections': inspection_stats,
            'lvFeeders': lv_feeder_stats,
            'region_filter': region_filter,
            'timestamp': timezone.now().isoformat()
        }

        return JsonResponse(response_data)

    except Exception as e:
        log_error(
            level='ERROR',
            message=f'DashboardStatisticsView.dashboard_statistics() failed: {type(e).__name__}: {str(e)}',
            user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None,
            traceback=str(e),
            ip_address=request.META.get('REMOTE_ADDR')
        )
        return JsonResponse({
            'error': 'Failed to generate dashboard statistics',
            'details': str(e)
        }, status=500)


@permission_classes([IsAuthenticated])
def get_transformer_analysis(request, analysis_type):
    if analysis_type == 'transformer_types':
        return get_transformer_types_data(request)
    elif analysis_type == 'capacity':
        return get_capacity_data(request)
    elif analysis_type == 'cooling_type':
        return get_cooling_type_data(request)
    elif analysis_type == 'manufacturer':
        return get_manufacturer_data(request)
    elif analysis_type == 'primary_voltage':
        return get_voltage_data(request)
    elif analysis_type == 'vector_group':
        return get_vector_group_data(request)
    elif analysis_type == 'manufacturing_year':
        return get_manufacturing_year_data(request)

    return JsonResponse({'error': 'Invalid analysis type'}, status=400)



def get_transformer_types_data(request):
    # Optimize query by using select_related and only necessary fields
    regions_with_counts = (
        Basestation.objects
        .values('region', 'csc')
        .annotate(
            total_count=Count('transformer_data_basestation'),
            conservator_count=Count(Case(
                When(transformer_data_basestation__trafo_type='Conservator', then=1),
                output_field=IntegerField(),
            )),
            hermetical_count=Count(Case(
                When(transformer_data_basestation__trafo_type='Hermatical', then=1),
                output_field=IntegerField(),
            )),
            compact_count=Count(Case(
                When(transformer_data_basestation__trafo_type='Compact', then=1),
                output_field=IntegerField(),
            ))
        )
        .order_by('region', 'csc')
    )

    # Use dictionary comprehension for faster processing
    region_dict = {}

    for entry in regions_with_counts:
        region = entry['region']
        csc = entry['csc']
        total = entry['total_count']

        if not region or not csc:
            continue  # Skip records with missing region or csc

        # Initialize region if not exists
        if region not in region_dict:
            region_dict[region] = {
                'key': region.lower(),
                'region': region,
                'csc': '',
                'conservator': 0,
                'hermetical': 0,
                'compact': 0,
                'conservatorPercentage': 0,
                'hermeticalPercentage': 0,
                'compactPercentage': 0,
                'children': []
            }

        # Update region totals
        region_dict[region]['conservator'] += entry['conservator_count']
        region_dict[region]['hermetical'] += entry['hermetical_count']
        region_dict[region]['compact'] += entry['compact_count']

        # Add CSC data if exists
        if csc:
            region_dict[region]['children'].append({
                'key': f"{region.lower()}-{csc.lower()}",
                'region': region,
                'csc': csc,
                'conservator': entry['conservator_count'],
                'hermetical': entry['hermetical_count'],
                'compact': entry['compact_count'],
                'conservatorPercentage': round((entry['conservator_count'] / total * 100) if total > 0 else 0),
                'hermeticalPercentage': round((entry['hermetical_count'] / total * 100) if total > 0 else 0),
                'compactPercentage': round((entry['compact_count'] / total * 100) if total > 0 else 0)
            })

    # Calculate region-level percentages and prepare final result
    result = []
    for region_data in region_dict.values():
        total = region_data['conservator'] + region_data['hermetical'] + region_data['compact']
        if total > 0:
            region_data['conservatorPercentage'] = round(region_data['conservator'] / total * 100)
            region_data['hermeticalPercentage'] = round(region_data['hermetical'] / total * 100)
            region_data['compactPercentage'] = round(region_data['compact'] / total * 100)
        result.append(region_data)

    return JsonResponse(result, safe=False)

def get_cooling_type_data(request):
    # Fetch all regions and CSCs with their associated cooling type counts in one query
    regions_with_counts = (
        Basestation.objects.values('region', 'csc')
        .annotate(
            total_count=Count('transformer_data_basestation'),
            onan_count=Count(Case(
                When(transformer_data_basestation__colling_type='ONAN', then=1),  # Changed from cooling_type to colling_type
                output_field=IntegerField()
            )),
            dry_type_count=Count(Case(
                When(transformer_data_basestation__colling_type='Dry Type', then=1),  # Changed from cooling_type to colling_type
                output_field=IntegerField()
            ))
        )
        .order_by('region', 'csc')
    )

    # Organize data into a nested structure (region -> CSC)
    region_dict = {}
    for entry in regions_with_counts:
        region = entry['region']
        csc = entry['csc']

        if not region or not csc:
            continue  # Skip records with missing region or csc

        if region not in region_dict:
            region_dict[region] = {
                'key': region.lower(),
                'region': region,
                'csc': '',
                'total_count': 0,
                'onan': 0,
                'dryType': 0,
                'children': []
            }

        # Aggregate totals at the region level
        region_dict[region]['total_count'] += entry['total_count']
        region_dict[region]['onan'] += entry['onan_count']
        region_dict[region]['dryType'] += entry['dry_type_count']

        # Add CSC-level data as children
        if csc:
            csc_data = {
                'key': f"{region.lower()}-{csc.lower()}",
                'region': region,
                'csc': csc,
                'onan': entry['onan_count'],
                'onanPercentage': round((entry['onan_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'dryType': entry['dry_type_count'],
                'dryTypePercentage': round((entry['dry_type_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0)
            }
            region_dict[region]['children'].append(csc_data)

    # Calculate percentages at the region level and prepare final result
    result = []
    for region_data in region_dict.values():
        region_data['onanPercentage'] = round((region_data['onan'] / region_data['total_count']) * 100 if region_data['total_count'] > 0 else 0)
        region_data['dryTypePercentage'] = round((region_data['dryType'] / region_data['total_count']) * 100 if region_data['total_count'] > 0 else 0)
        # Remove total_count from final output if not needed in frontend
        del region_data['total_count']
        result.append(region_data)

    return JsonResponse(result, safe=False)

def get_capacity_data(request):
    regions_with_counts = (
        Basestation.objects.values('region', 'csc')
        .annotate(
            total_count=Count('transformer_data_basestation'),
            kva10_count=Count(Case(
                When(transformer_data_basestation__capacity='10', then=1),
                output_field=IntegerField()
            )),
            kva25_count=Count(Case(
                When(transformer_data_basestation__capacity='25', then=1),
                output_field=IntegerField()
            )),
            kva50_count=Count(Case(
                When(transformer_data_basestation__capacity='50', then=1),
                output_field=IntegerField()
            )),
            kva100_count=Count(Case(
                When(transformer_data_basestation__capacity='100', then=1),
                output_field=IntegerField()
            )),
            kva200_count=Count(Case(
                When(transformer_data_basestation__capacity='200', then=1),
                output_field=IntegerField()
            )),
            kva315_count=Count(Case(
                When(transformer_data_basestation__capacity='315', then=1),
                output_field=IntegerField()
            )),
            kva400_count=Count(Case(
                When(transformer_data_basestation__capacity='400', then=1),
                output_field=IntegerField()
            )),
            kva500_count=Count(Case(
                When(transformer_data_basestation__capacity='500', then=1),
                output_field=IntegerField()
            )),
            kva630_count=Count(Case(
                When(transformer_data_basestation__capacity='630', then=1),
                output_field=IntegerField()
            )),
            kva800_count=Count(Case(
                When(transformer_data_basestation__capacity='800', then=1),
                output_field=IntegerField()
            )),
            kva1250_count=Count(Case(
                When(transformer_data_basestation__capacity='1250', then=1),
                output_field=IntegerField()
            )),
            kva2500_count=Count(Case(
                When(transformer_data_basestation__capacity='2500', then=1),
                output_field=IntegerField()
            )),
            other_count=Count(Case(
                When(transformer_data_basestation__capacity='null', then=1),
                output_field=IntegerField()
            ))
        )
        .order_by('region', 'csc')
    )

    region_dict = {}
    for entry in regions_with_counts:
        region = entry['region']
        csc = entry['csc']

        if not region or not csc:
            continue  # Skip records with missing region or csc

        if region not in region_dict:
            region_dict[region] = {
                'key': region.lower(),
                'region': region,
                'csc': '',
                'total_count': 0,
                'kva10': 0,
                'kva25': 0,
                'kva50': 0,
                'kva100': 0,
                'kva200': 0,
                'kva315': 0,
                'kva400': 0,
                'kva500': 0,
                'kva630': 0,
                'kva800': 0,
                'kva1250': 0,
                'kva2500': 0,
                'other': 0,
                'children': []
            }

        region_dict[region]['total_count'] += entry['total_count']
        region_dict[region]['kva10'] += entry['kva10_count']
        region_dict[region]['kva25'] += entry['kva25_count']
        region_dict[region]['kva50'] += entry['kva50_count']
        region_dict[region]['kva100'] += entry['kva100_count']
        region_dict[region]['kva200'] += entry['kva200_count']
        region_dict[region]['kva315'] += entry['kva315_count']
        region_dict[region]['kva400'] += entry['kva400_count']
        region_dict[region]['kva500'] += entry['kva500_count']
        region_dict[region]['kva630'] += entry['kva630_count']
        region_dict[region]['kva800'] += entry['kva800_count']
        region_dict[region]['kva1250'] += entry['kva1250_count']
        region_dict[region]['kva2500'] += entry['kva2500_count']
        region_dict[region]['other'] += entry['other_count']

        if csc:
            csc_data = {
                'key': f"{region.lower()}-{csc.lower()}",
                'region': region,
                'csc': csc,
                'kva10': entry['kva10_count'],
                'kva10Percentage': round((entry['kva10_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'kva25': entry['kva25_count'],
                'kva25Percentage': round((entry['kva25_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'kva50': entry['kva50_count'],
                'kva50Percentage': round((entry['kva50_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'kva100': entry['kva100_count'],
                'kva100Percentage': round((entry['kva100_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'kva200': entry['kva200_count'],
                'kva200Percentage': round((entry['kva200_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'kva315': entry['kva315_count'],
                'kva315Percentage': round((entry['kva315_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'kva400': entry['kva400_count'],
                'kva400Percentage': round((entry['kva400_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'kva500': entry['kva500_count'],
                'kva500Percentage': round((entry['kva500_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'kva630': entry['kva630_count'],
                'kva630Percentage': round((entry['kva630_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'kva800': entry['kva800_count'],
                'kva800Percentage': round((entry['kva800_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'kva1250': entry['kva1250_count'],
                'kva2500': entry['kva2500_count'],
                'kva2500Percentage': round((entry['kva2500_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'other': entry['other_count'],
                'otherPercentage': round((entry['other_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0)
            }
            region_dict[region]['children'].append(csc_data)

    result = []
    for region_data in region_dict.values():
        total = region_data['total_count']
        region_data['kva10Percentage'] = round((region_data['kva10'] / total) * 100 if total > 0 else 0)
        region_data['kva25Percentage'] = round((region_data['kva25'] / total) * 100 if total > 0 else 0)
        region_data['kva50Percentage'] = round((region_data['kva50'] / total) * 100 if total > 0 else 0)
        region_data['kva100Percentage'] = round((region_data['kva100'] / total) * 100 if total > 0 else 0)
        region_data['kva200Percentage'] = round((region_data['kva200'] / total) * 100 if total > 0 else 0)
        region_data['kva315Percentage'] = round((region_data['kva315'] / total) * 100 if total > 0 else 0)
        region_data['kva400Percentage'] = round((region_data['kva400'] / total) * 100 if total > 0 else 0)
        region_data['kva500Percentage'] = round((region_data['kva500'] / total) * 100 if total > 0 else 0)
        region_data['kva630Percentage'] = round((region_data['kva630'] / total) * 100 if total > 0 else 0)
        region_data['kva800Percentage'] = round((region_data['kva800'] / total) * 100 if total > 0 else 0)
        region_data['kva1250Percentage'] = round((region_data['kva1250'] / total) * 100 if total > 0 else 0)
        region_data['kva2500Percentage'] = round((region_data['kva2500'] / total) * 100 if total > 0 else 0)
        region_data['otherPercentage'] = round((region_data['other'] / total) * 100 if total > 0 else 0)
        del region_data['total_count']
        result.append(region_data)

    return JsonResponse(result, safe=False)

def get_manufacturer_data(request):
    regions_with_counts = (
        Basestation.objects.values('region', 'csc')
        .annotate(
            total_count=Count('transformer_data_basestation'),
            abb_count=Count(Case(
                When(transformer_data_basestation__manufacturer='ABB Tanzania', then=1),
                output_field=IntegerField()
            )),
            apex_count=Count(Case(
                When(transformer_data_basestation__manufacturer='Apex', then=1),
                output_field=IntegerField()
            ))
        )
        .order_by('region', 'csc')
    )

    region_dict = {}
    for entry in regions_with_counts:
        region = entry['region']
        csc = entry['csc']

        if not region or not csc:
            continue  # Skip records with missing region or csc

        if region not in region_dict:
            region_dict[region] = {
                'key': region.lower(),
                'region': region,
                'csc': '',
                'total_count': 0,
                'abbTanzania': 0,
                'apex': 0,
                'children': []
            }

        region_dict[region]['total_count'] += entry['total_count']
        region_dict[region]['abbTanzania'] += entry['abb_count']
        region_dict[region]['apex'] += entry['apex_count']

        if csc:
            csc_data = {
                'key': f"{region.lower()}-{csc.lower()}",
                'region': region,
                'csc': csc,
                'abbTanzania': entry['abb_count'],
                'abbTanzaniaPercentage': round((entry['abb_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'apex': entry['apex_count'],
                'apexPercentage': round((entry['apex_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0)
            }
            region_dict[region]['children'].append(csc_data)

    result = []
    for region_data in region_dict.values():
        total = region_data['total_count']
        region_data['abbTanzaniaPercentage'] = round((region_data['abbTanzania'] / total) * 100 if total > 0 else 0)
        region_data['apexPercentage'] = round((region_data['apex'] / total) * 100 if total > 0 else 0)
        del region_data['total_count']
        result.append(region_data)

    return JsonResponse(result, safe=False)

def get_voltage_data(request):
    regions_with_counts = (
        Basestation.objects.values('region', 'csc')
        .annotate(
            total_count=Count('transformer_data_basestation'),
            v15_count=Count(Case(
                When(transformer_data_basestation__primary_voltage='15', then=1),
                output_field=IntegerField()
            )),
            v19_count=Count(Case(
                When(transformer_data_basestation__primary_voltage='19', then=1),
                output_field=IntegerField()
            )),
            v33_count=Count(Case(
                When(transformer_data_basestation__primary_voltage='33', then=1),
                output_field=IntegerField()
            )),
            other_count=Count(Case(
                When(transformer_data_basestation__primary_voltage='null', then=1),
                output_field=IntegerField()
            ))
        )
        .order_by('region', 'csc')
    )

    region_dict = {}
    for entry in regions_with_counts:
        region = entry['region']
        csc = entry['csc']

        if not region or not csc:
            continue  # Skip records with missing region or csc

        if region not in region_dict:
            region_dict[region] = {
                'key': region.lower(),
                'region': region,
                'csc': '',
                'total_count': 0,
                'v15': 0,
                'v19': 0,
                'v33': 0,
                'other': 0,
                'children': []
            }

        region_dict[region]['total_count'] += entry['total_count']
        region_dict[region]['v15'] += entry['v15_count']
        region_dict[region]['v19'] += entry['v19_count']
        region_dict[region]['v33'] += entry['v33_count']
        region_dict[region]['other'] += entry['other_count']

        if csc:
            csc_data = {
                'key': f"{region.lower()}-{csc.lower()}",
                'region': region,
                'csc': csc,
                'v15': entry['v15_count'],
                'v15Percentage': round((entry['v15_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'v19': entry['v19_count'],
                'v19Percentage': round((entry['v19_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'v33': entry['v33_count'],
                'v33Percentage': round((entry['v33_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'other': entry['other_count'],
                'otherPercentage': round((entry['other_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0)
            }
            region_dict[region]['children'].append(csc_data)

    result = []
    for region_data in region_dict.values():
        total = region_data['total_count']
        region_data['v15Percentage'] = round((region_data['v15'] / total) * 100 if total > 0 else 0)
        region_data['v19Percentage'] = round((region_data['v19'] / total) * 100 if total > 0 else 0)
        region_data['v33Percentage'] = round((region_data['v33'] / total) * 100 if total > 0 else 0)
        region_data['otherPercentage'] = round((region_data['other'] / total) * 100 if total > 0 else 0)
        del region_data['total_count']
        result.append(region_data)

    return JsonResponse(result, safe=False)

def get_vector_group_data(request):
    regions_with_counts = (
        Basestation.objects.values('region', 'csc')
        .annotate(
            total_count=Count('transformer_data_basestation'),
            dy1_count=Count(Case(
                When(transformer_data_basestation__vector_group='DY1', then=1),
                output_field=IntegerField()
            )),
            dy5_count=Count(Case(
                When(transformer_data_basestation__vector_group='DY5', then=1),
                output_field=IntegerField()
            )),
            dy11_count=Count(Case(
                When(transformer_data_basestation__vector_group='DY11', then=1),
                output_field=IntegerField()
            )),
            other_count=Count(Case(
                When(transformer_data_basestation__vector_group='Other', then=1),
                output_field=IntegerField()
            ))
        )
        .order_by('region', 'csc')
    )

    region_dict = {}
    for entry in regions_with_counts:
        region = entry['region']
        csc = entry['csc']

        if not region or not csc:
            continue  # Skip records with missing region or csc

        if region not in region_dict:
            region_dict[region] = {
                'key': region.lower(),
                'region': region,
                'csc': '',
                'total_count': 0,
                'dy1': 0,
                'dy5': 0,
                'dy11': 0,
                'other': 0,
                'children': []
            }

        region_dict[region]['total_count'] += entry['total_count']
        region_dict[region]['dy1'] += entry['dy1_count']
        region_dict[region]['dy5'] += entry['dy5_count']
        region_dict[region]['dy11'] += entry['dy11_count']
        region_dict[region]['other'] += entry['other_count']

        if csc:
            csc_data = {
                'key': f"{region.lower()}-{csc.lower()}",
                'region': region,
                'csc': csc,
                'dy1': entry['dy1_count'],
                'dy1Percentage': round((entry['dy1_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'dy5': entry['dy5_count'],
                'dy5Percentage': round((entry['dy5_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'dy11': entry['dy11_count'],
                'dy11Percentage': round((entry['dy11_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'other': entry['other_count'],
                'otherPercentage': round((entry['other_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0)
            }
            region_dict[region]['children'].append(csc_data)

    result = []
    for region_data in region_dict.values():
        total = region_data['total_count']
        region_data['dy1Percentage'] = round((region_data['dy1'] / total) * 100 if total > 0 else 0)
        region_data['dy5Percentage'] = round((region_data['dy5'] / total) * 100 if total > 0 else 0)
        region_data['dy11Percentage'] = round((region_data['dy11'] / total) * 100 if total > 0 else 0)
        region_data['otherPercentage'] = round((region_data['other'] / total) * 100 if total > 0 else 0)
        del region_data['total_count']
        result.append(region_data)

    return JsonResponse(result, safe=False)

def get_manufacturing_year_data(request):
    regions_with_counts = (
        Basestation.objects.values('region', 'csc')
        .annotate(
            total_count=Count('transformer_data_basestation'),
            y2020_count=Count(Case(
                When(transformer_data_basestation__manufacturing_year='2020', then=1),
                output_field=IntegerField()
            )),
            y2021_count=Count(Case(
                When(transformer_data_basestation__manufacturing_year='2021', then=1),
                output_field=IntegerField()
            )),
            y2022_count=Count(Case(
                When(transformer_data_basestation__manufacturing_year='2022', then=1),
                output_field=IntegerField()
            ))
        )
        .order_by('region', 'csc')
    )

    region_dict = {}
    for entry in regions_with_counts:
        region = entry['region']
        csc = entry['csc']

        if not region or not csc:
            continue  # Skip records with missing region or csc

        if region not in region_dict:
            region_dict[region] = {
                'key': region.lower(),
                'region': region,
                'csc': '',
                'total_count': 0,
                'y2020': 0,
                'y2021': 0,
                'y2022': 0,
                'children': []
            }

        region_dict[region]['total_count'] += entry['total_count']
        region_dict[region]['y2020'] += entry['y2020_count']
        region_dict[region]['y2021'] += entry['y2021_count']
        region_dict[region]['y2022'] += entry['y2022_count']

        if csc:
            csc_data = {
                'key': f"{region.lower()}-{csc.lower()}",
                'region': region,
                'csc': csc,
                'y2020': entry['y2020_count'],
                'y2020Percentage': round((entry['y2020_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'y2021': entry['y2021_count'],
                'y2021Percentage': round((entry['y2021_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0),
                'y2022': entry['y2022_count'],
                'y2022Percentage': round((entry['y2022_count'] / entry['total_count']) * 100 if entry['total_count'] > 0 else 0)
            }
            region_dict[region]['children'].append(csc_data)

    result = []
    for region_data in region_dict.values():
        total = region_data['total_count']
        region_data['y2020Percentage'] = round((region_data['y2020'] / total) * 100 if total > 0 else 0)
        region_data['y2021Percentage'] = round((region_data['y2021'] / total) * 100 if total > 0 else 0)
        region_data['y2022Percentage'] = round((region_data['y2022'] / total) * 100 if total > 0 else 0)
        del region_data['total_count']
        result.append(region_data)

    return JsonResponse(result, safe=False)


@permission_classes([IsAuthenticated])
def get_inspection_analysis(request, inspection_type):
    """
    Get analysis data for inspections based on the specified analysis type.
    """
    inspection_types = [
        'voltage_phase_unbalance', 'average_voltage',
        'body_condition', 'arrester', 'drop_out', 'fuse_link', 'mv_bushing',
        'mv_cable_lug', 'lv_bushing', 'lv_cable_lug', 'oil_level', 'insulation_level',
        'horn_gap', 'silica_gel', 'has_linkage', 'arrester_body_ground',
        'neutral_ground', 'status_of_mounting', 'mounting_condition',
        'total_transformer_load'  # Add this to the list of valid inspection types
    ]

    if inspection_type not in inspection_types:
        return JsonResponse({'error': f'Invalid inspection type: {inspection_type}'}, status=400)

    # Get the latest inspection IDs
    latest_inspections = Inspection.objects.values('transformer_data').annotate(
        latest_id=Max('id')
    )

    # Special handling for voltage calculations and total_transformer_load
    if inspection_type in ['voltage_phase_unbalance', 'average_voltage', 'total_transformer_load']:
        # Prepare query fields based on analysis type
        query_fields = {
            'transformer_data__basestation__region',
            'transformer_data__basestation__csc',
        }

        # Add specific fields based on analysis type
        if inspection_type == 'voltage_phase_unbalance':
            query_fields.add('voltage_phase_unbalance')
        elif inspection_type == 'average_voltage':
            query_fields.add('average_voltage')
        elif inspection_type == 'total_transformer_load':
            query_fields.add('total_transformer_load')

        # Build the query
        inspections = (
            Inspection.objects
            .filter(id__in=[item['latest_id'] for item in latest_inspections])
            .select_related('transformer_data__basestation')
            .values(*query_fields)
        )

        # Add exclusion for null values based on analysis type
        if inspection_type == 'voltage_phase_unbalance':
            inspections = inspections.exclude(voltage_phase_unbalance__isnull=True)
        elif inspection_type == 'average_voltage':
            inspections = inspections.exclude(average_voltage__isnull=True)
        elif inspection_type == 'total_transformer_load':
            inspections = inspections.exclude(total_transformer_load__isnull=True)

        region_data = {}
        for inspection in inspections:
            region = inspection['transformer_data__basestation__region']
            csc = inspection['transformer_data__basestation__csc']

            if not region or not csc:
               continue

            try:
                # Get value based on analysis type
                if inspection_type == 'voltage_phase_unbalance':
                    value = float(inspection['voltage_phase_unbalance'] or 0)
                elif inspection_type == 'average_voltage':
                    value = float(inspection['average_voltage'] or 0)
                elif inspection_type == 'total_transformer_load':
                    value = float(inspection['total_transformer_load'] or 0)
                else:
                    continue

                if not region or not csc:
                    continue  # Skip records with missing region or csc

                # Initialize region if not exists
                if region not in region_data:
                    region_data[region] = {
                        'key': region.lower(),
                        'region': region,
                        'csc': '',
                        'children': []
                    }

                    # Initialize counters based on analysis type
                    if inspection_type == 'voltage_phase_unbalance':
                        region_data[region].update({'balanced': 0, 'unbalanced': 0})
                    elif inspection_type == 'average_voltage':
                        region_data[region].update({'underVoltage': 0, 'normal': 0, 'overVoltage': 0})
                    elif inspection_type == 'total_transformer_load':
                        region_data[region].update({
                            'below20': 0,
                            'load20_50': 0,
                            'load50_80': 0,
                            'load80_100': 0,
                            'above100': 0
                        })

                # Initialize CSC if not exists
                csc_key = f"{region.lower()}-{csc.lower()}"
                csc_entry = next(
                    (c for c in region_data[region]['children'] if c['key'] == csc_key),
                    None
                )

                if not csc_entry:
                    csc_entry = {
                        'key': csc_key,
                        'region': region,
                        'csc': csc
                    }

                    # Initialize counters based on analysis type
                    if inspection_type == 'voltage_phase_unbalance':
                        csc_entry.update({'balanced': 0, 'unbalanced': 0})
                    elif inspection_type == 'average_voltage':
                        csc_entry.update({'underVoltage': 0, 'normal': 0, 'overVoltage': 0})
                    elif inspection_type == 'total_transformer_load':
                        csc_entry.update({
                            'below20': 0,
                            'load20_50': 0,
                            'load50_80': 0,
                            'load80_100': 0,
                            'above100': 0
                        })

                    region_data[region]['children'].append(csc_entry)

                # Update counters based on analysis type and value
                if inspection_type == 'voltage_phase_unbalance':
                    if value <= 3:
                        region_data[region]['balanced'] += 1
                        csc_entry['balanced'] += 1
                    else:
                        region_data[region]['unbalanced'] += 1
                        csc_entry['unbalanced'] += 1
                elif inspection_type == 'average_voltage':
                    if value < 360:
                        region_data[region]['underVoltage'] += 1
                        csc_entry['underVoltage'] += 1
                    elif value <= 440:
                        region_data[region]['normal'] += 1
                        csc_entry['normal'] += 1
                    else:
                        region_data[region]['overVoltage'] += 1
                        csc_entry['overVoltage'] += 1
                elif inspection_type == 'total_transformer_load':
                    if value < 20:
                        region_data[region]['below20'] += 1
                        csc_entry['below20'] += 1
                    elif value < 50:
                        region_data[region]['load20_50'] += 1
                        csc_entry['load20_50'] += 1
                    elif value < 80:
                        region_data[region]['load50_80'] += 1
                        csc_entry['load50_80'] += 1
                    elif value < 100:
                        region_data[region]['load80_100'] += 1
                        csc_entry['load80_100'] += 1
                    else:
                        region_data[region]['above100'] += 1
                        csc_entry['above100'] += 1

            except (ValueError, TypeError):
                continue

        # Calculate percentages
        result = []
        for region, data in region_data.items():
            if inspection_type == 'voltage_phase_unbalance':
                total = data['balanced'] + data['unbalanced']
                if total > 0:
                    data['balancedPercentage'] = round((data['balanced'] / total) * 100)
                    data['unbalancedPercentage'] = round((data['unbalanced'] / total) * 100)

                for child in data['children']:
                    child_total = child['balanced'] + child['unbalanced']
                    if child_total > 0:
                        child['balancedPercentage'] = round((child['balanced'] / child_total) * 100)
                        child['unbalancedPercentage'] = round((child['unbalanced'] / child_total) * 100)
            elif inspection_type == 'average_voltage':
                total = data['underVoltage'] + data['normal'] + data['overVoltage']
                if total > 0:
                    data['underVoltagePercentage'] = round((data['underVoltage'] / total) * 100)
                    data['normalPercentage'] = round((data['normal'] / total) * 100)
                    data['overVoltagePercentage'] = round((data['overVoltage'] / total) * 100)

                for child in data['children']:
                    child_total = child['underVoltage'] + child['normal'] + child['overVoltage']
                    if child_total > 0:
                        child['underVoltagePercentage'] = round((child['underVoltage'] / child_total) * 100)
                        child['normalPercentage'] = round((child['normal'] / child_total) * 100)
                        child['overVoltagePercentage'] = round((child['overVoltage'] / child_total) * 100)
            elif inspection_type == 'total_transformer_load':
                total = data['below20'] + data['load20_50'] + data['load50_80'] + data['load80_100'] + data['above100']
                if total > 0:
                    data['below20Percentage'] = round((data['below20'] / total) * 100)
                    data['load20_50Percentage'] = round((data['load20_50'] / total) * 100)
                    data['load50_80Percentage'] = round((data['load50_80'] / total) * 100)
                    data['load80_100Percentage'] = round((data['load80_100'] / total) * 100)
                    data['above100Percentage'] = round((data['above100'] / total) * 100)

                for child in data['children']:
                    child_total = child['below20'] + child['load20_50'] + child['load50_80'] + child['load80_100'] + child['above100']
                    if child_total > 0:
                        child['below20Percentage'] = round((child['below20'] / child_total) * 100)
                        child['load20_50Percentage'] = round((child['load20_50'] / child_total) * 100)
                        child['load50_80Percentage'] = round((child['load50_80'] / child_total) * 100)
                        child['load80_100Percentage'] = round((child['load80_100'] / child_total) * 100)
                        child['above100Percentage'] = round((child['above100'] / child_total) * 100)

            result.append(data)

        return JsonResponse(result, safe=False)

    # ... rest of the existing code for other inspection types ... (omitted for brevity)

    # Map frontend field names to database field names
    field_mapping = {
        'voltage_phase_unbalance': 'voltage_phase_unbalance',
        'body_condition': 'body_condition',
        'arrester': 'arrester',
        'drop_out': 'drop_out',
        'fuse_link': 'fuse_link',
        'mv_bushing': 'mv_bushing',  # Maps to bushing in the database
        'mv_cable_lug': 'mv_cable_lug',  # Maps to cable_lugs in the database
        'lv_bushing': 'lv_bushing',  # Maps to bushing in the database
        'lv_cable_lug': 'lv_cable_lug',  # Maps to cable_lugs in the database
        'oil_level': 'oil_level',
        'insulation_level': 'insulation_level',
        'horn_gap': 'horn_gap',
        'silica_gel': 'silica_gel',
        'has_linkage': 'has_linkage',
        'arrester_body_ground': 'arrester_body_ground',
        'neutral_ground': 'neutral_ground',
        'status_of_mounting': 'status_of_mounting',
        'mounting_condition': 'mounting_condition'
    }

    db_field = field_mapping.get(inspection_type)

    # Get only the latest inspection for each transformer
    # print(f"Querying latest inspections with field: {db_field}")

    # First, get the latest inspection ID for each transformer
    latest_inspections = Inspection.objects.values('transformer_data').annotate(
        latest_id=Max('id')
    )

    # Then, get the actual inspection records using those IDs
    inspections = Inspection.objects.filter(
        id__in=[item['latest_id'] for item in latest_inspections]
    ).select_related(
        'transformer_data__basestation'
    ).values(
        'transformer_data__basestation__region',
        'transformer_data__basestation__csc',
        db_field
    ).exclude(Q(**{db_field + '__isnull': True}) | Q(**{db_field: ''}))

    print(f"Found {inspections.count()} latest inspection records with {db_field} data")

    # Aggregate data by region and CSC
    region_data = {}

    for inspection in inspections:
        region = inspection['transformer_data__basestation__region']
        csc = inspection['transformer_data__basestation__csc']
        value = inspection[db_field]

        if not region or not csc:
            continue  # Skip records with missing region or csc

        # Initialize region if not exists
        if region not in region_data:
            region_data[region] = {
                'key': region.lower(),
                'region': region,
                'csc': '',
                'children': [],
                # Initialize counters based on inspection type
            }
            initialize_counters(region_data[region], inspection_type)

        # Initialize CSC if not exists
        csc_key = f"{region.lower()}-{csc.lower()}"
        csc_entry = next((c for c in region_data[region]['children'] if c['key'] == csc_key), None)

        if not csc_entry:
            csc_entry = {
                'key': csc_key,
                'region': region,
                'csc': csc,
            }
            initialize_counters(csc_entry, inspection_type)
            region_data[region]['children'].append(csc_entry)

        # Update counters based on inspection type and value
        update_counters(region_data[region], inspection_type, value)
        update_counters(csc_entry, inspection_type, value)

    # Calculate percentages for each region and CSC
    result = []
    for region, data in region_data.items():
        calculate_percentages(data, inspection_type)
        for child in data['children']:
            calculate_percentages(child, inspection_type)
        result.append(data)

    print(f"Returning {len(result)} regions with data for {inspection_type}")
    if result:
        print(f"Sample data for first region: {result[0]}")

    return JsonResponse(result, safe=False)


def initialize_counters(data_dict, analysis_type):
    """Initialize counters based on analysis type"""
    if analysis_type == 'voltage_phase_unbalance':
        data_dict.update({'balanced': 0, 'unbalanced': 0})
    elif analysis_type in ['body_condition', 'horn_gap', 'silica_gel', 'status_of_mounting', 'mounting_condition']:
        data_dict.update({'good': 0, 'fair': 0, 'poor': 0})
    elif analysis_type in ['arrester', 'drop_out', 'fuse_link', 'mv_bushing', 'mv_cable_lug', 'lv_bushing', 'lv_cable_lug']:
        data_dict.update({'ok': 0, 'oneMissed': 0, 'twoMissed': 0, 'allMissed': 0})
    elif analysis_type == 'oil_level':
        data_dict.update({'full': 0, 'threeFourths': 0, 'half': 0, 'oneFourth': 0})
    elif analysis_type == 'insulation_level':
        data_dict.update({'acceptable': 0, 'notAcceptable': 0})
    elif analysis_type == 'has_linkage':
        data_dict.update({'yes': 0, 'no': 0})
    elif analysis_type in ['arrester_body_ground', 'neutral_ground']:
        data_dict.update({'available': 0, 'notAvailable': 0})
    elif analysis_type == 'total_transformer_load':
        data_dict.update({'below20': 0, 'load20_50': 0, 'load50_80': 0, 'load80_100': 0, 'above100': 0})


def update_counters(data_dict, analysis_type, value):
    """Update counters based on analysis type and value"""
    original_value = value
    value = value.lower() if isinstance(value, str) else value
    print(f"Updating counters for {analysis_type}, value: {original_value} (normalized to: {value})")

    if analysis_type == 'voltage_phase_unbalance':
        try:
            value = float(value)
            if value <= 5:
                data_dict['balanced'] += 1
            else:
                data_dict['unbalanced'] += 1
        except (TypeError, ValueError):
            pass
    elif analysis_type in ['body_condition', 'horn_gap', 'silica_gel', 'status_of_mounting', 'mounting_condition']:
        if value == 'good':
            data_dict['good'] += 1
        elif value == 'fair':
            data_dict['fair'] += 1
        elif value == 'poor':
            data_dict['poor'] += 1
    elif analysis_type in ['arrester', 'drop_out', 'fuse_link', 'mv_bushing', 'mv_cable_lug', 'lv_bushing', 'lv_cable_lug']:
        if value == 'ok':
            data_dict['ok'] += 1
        elif value == 'one missed':
            data_dict['oneMissed'] += 1
        elif value == 'two missed':
            data_dict['twoMissed'] += 1
        elif value == 'all missed':
            data_dict['allMissed'] += 1
    elif analysis_type == 'oil_level':
        if value == 'full':
            data_dict['full'] += 1
        elif value == '0.75':
            data_dict['threeFourths'] += 1
        elif value == '0.5':
            data_dict['half'] += 1
        elif value == '0.25':
            data_dict['oneFourth'] += 1
    elif analysis_type == 'insulation_level':
        if value == 'acceptable':
            data_dict['acceptable'] += 1
        elif value == 'not acceptable':
            data_dict['notAcceptable'] += 1
    elif analysis_type == 'has_linkage':
        if value == 'yes':
            data_dict['yes'] += 1
        elif value == 'no':
            data_dict['no'] += 1
    elif analysis_type in ['arrester_body_ground', 'neutral_ground']:
        if value == 'available':
            data_dict['available'] += 1
        elif value == 'not available':
            data_dict['notAvailable'] += 1
    elif analysis_type == 'total_transformer_load':
        try:
            load_percentage = float(value)
            if load_percentage < 20:
                data_dict['below20'] += 1
            elif load_percentage < 50:
                data_dict['load20_50'] += 1
            elif load_percentage < 80:
                data_dict['load50_80'] += 1
            elif load_percentage < 100:
                data_dict['load80_100'] += 1
            else:
                data_dict['above100'] += 1
        except (TypeError, ValueError):
            pass


def calculate_percentages(data_dict, analysis_type):
    """Calculate percentages for each counter"""
    if analysis_type == 'voltage_phase_unbalance':
        total = data_dict['balanced'] + data_dict['unbalanced']
        if total > 0:
            data_dict['balancedPercentage'] = round((data_dict['balanced'] / total) * 100)
            data_dict['unbalancedPercentage'] = round((data_dict['unbalanced'] / total) * 100)
    elif analysis_type in ['body_condition', 'horn_gap', 'silica_gel', 'status_of_mounting', 'mounting_condition']:
        total = data_dict['good'] + data_dict['fair'] + data_dict['poor']
        if total > 0:
            data_dict['goodPercentage'] = round((data_dict['good'] / total) * 100)
            data_dict['fairPercentage'] = round((data_dict['fair'] / total) * 100)
            data_dict['poorPercentage'] = round((data_dict['poor'] / total) * 100)
    elif analysis_type in ['arrester', 'drop_out', 'fuse_link', 'mv_bushing', 'mv_cable_lug', 'lv_bushing', 'lv_cable_lug']:
        total = data_dict['ok'] + data_dict['oneMissed'] + data_dict['twoMissed'] + data_dict['allMissed']
        if total > 0:
            data_dict['okPercentage'] = round((data_dict['ok'] / total) * 100)
            data_dict['oneMissedPercentage'] = round((data_dict['oneMissed'] / total) * 100)
            data_dict['twoMissedPercentage'] = round((data_dict['twoMissed'] / total) * 100)
            data_dict['allMissedPercentage'] = round((data_dict['allMissed'] / total) * 100)
    elif analysis_type == 'oil_level':
        total = data_dict['full'] + data_dict['threeFourths'] + data_dict['half'] + data_dict['oneFourth']
        if total > 0:
            data_dict['fullPercentage'] = round((data_dict['full'] / total) * 100)
            data_dict['threeFourthsPercentage'] = round((data_dict['threeFourths'] / total) * 100)
            data_dict['halfPercentage'] = round((data_dict['half'] / total) * 100)
            data_dict['oneFourthPercentage'] = round((data_dict['oneFourth'] / total) * 100)
    elif analysis_type == 'insulation_level':
        total = data_dict['acceptable'] + data_dict['notAcceptable']
        if total > 0:
            data_dict['acceptablePercentage'] = round((data_dict['acceptable'] / total) * 100)
            data_dict['notAcceptablePercentage'] = round((data_dict['notAcceptable'] / total) * 100)
    elif analysis_type == 'has_linkage':
        total = data_dict['yes'] + data_dict['no']
        if total > 0:
            data_dict['yesPercentage'] = round((data_dict['yes'] / total) * 100)
            data_dict['noPercentage'] = round((data_dict['no'] / total) * 100)
    elif analysis_type in ['arrester_body_ground', 'neutral_ground']:
        total = data_dict['available'] + data_dict['notAvailable']
        if total > 0:
            data_dict['availablePercentage'] = round((data_dict['available'] / total) * 100)
            data_dict['notAvailablePercentage'] = round((data_dict['notAvailable'] / total) * 100)
    elif analysis_type == 'total_transformer_load':
        total = (data_dict['below20'] + data_dict['load20_50'] + data_dict['load50_80'] +
                 data_dict['load80_100'] + data_dict['above100'])
        if total > 0:
            data_dict['below20Percentage'] = round((data_dict['below20'] / total) * 100)
            data_dict['load20_50Percentage'] = round((data_dict['load20_50'] / total) * 100)
            data_dict['load50_80Percentage'] = round((data_dict['load50_80'] / total) * 100)
            data_dict['load80_100Percentage'] = round((data_dict['load80_100'] / total) * 100)
            data_dict['above100Percentage'] = round((data_dict['above100'] / total) * 100)



@permission_classes([IsAuthenticated])
def get_lvfeeder_analysis(request, analysis_type):
    """Handle LV Feeder analysis requests for different analysis types"""
    analysis_types = [
        'transformer_load',
        'current_phase_unbalance',
        'percentage_of_neutral'
    ]

    if analysis_type not in analysis_types:
        return JsonResponse({'error': f'Invalid analysis type: {analysis_type}'}, status=400)

    # Get the latest inspection IDs for each transformer
    latest_inspections = Inspection.objects.values('transformer_data').annotate(
        latest_id=Max('id')
    )

    if analysis_type == 'transformer_load':
        # Get LvFeeder data directly
        lv_feeders = (
            LvFeeder.objects
            .filter(inspection_data_id__in=[item['latest_id'] for item in latest_inspections])
            .select_related('inspection_data__transformer_data__basestation')
            .values(
                'inspection_data__transformer_data__basestation__region',
                'inspection_data__transformer_data__basestation__csc',
                'transformer_load'
            )
            .exclude(transformer_load__isnull=True)
        )

        region_data = {}
        for feeder in lv_feeders:
            region = feeder['inspection_data__transformer_data__basestation__region']
            csc = feeder['inspection_data__transformer_data__basestation__csc']
            load_percentage = float(feeder['transformer_load'] or 0)

            if not region or not csc:
                continue  # Skip records with missing region or csc

            # Initialize region if not exists
            if region not in region_data:
                region_data[region] = {
                    'key': region.lower(),
                    'region': region,
                    'csc': '',
                    'below20': 0,
                    'load20_50': 0,
                    'load50_80': 0,
                    'load80_100': 0,
                    'above100': 0,
                    'children': []
                }

            # Update counters based on load percentage
            if load_percentage < 20:
                region_data[region]['below20'] += 1
            elif load_percentage < 50:
                region_data[region]['load20_50'] += 1
            elif load_percentage < 80:
                region_data[region]['load50_80'] += 1
            elif load_percentage < 100:
                region_data[region]['load80_100'] += 1
            else:
                region_data[region]['above100'] += 1

            # Handle CSC data similarly...
            csc_key = f"{region.lower()}-{csc.lower()}"
            csc_entry = next(
                (c for c in region_data[region]['children'] if c['key'] == csc_key),
                None
            )

            if not csc_entry:
                csc_entry = {
                    'key': csc_key,
                    'region': region,
                    'csc': csc,
                    'below20': 0,
                    'load20_50': 0,
                    'load50_80': 0,
                    'load80_100': 0,
                    'above100': 0
                }
                region_data[region]['children'].append(csc_entry)

            if load_percentage < 20:
                csc_entry['below20'] += 1
            elif load_percentage < 50:
                csc_entry['load20_50'] += 1
            elif load_percentage < 80:
                csc_entry['load50_80'] += 1
            elif load_percentage < 100:
                csc_entry['load80_100'] += 1
            else:
                csc_entry['above100'] += 1

    elif analysis_type == 'current_phase_unbalance':
        # Get LvFeeder data directly
        lv_feeders = (
            LvFeeder.objects
            .filter(inspection_data_id__in=[item['latest_id'] for item in latest_inspections])
            .select_related('inspection_data__transformer_data__basestation')
            .values(
                'inspection_data__transformer_data__basestation__region',
                'inspection_data__transformer_data__basestation__csc',
                'current_phase_unbalance'
            )
            .exclude(current_phase_unbalance__isnull=True)
        )

        region_data = {}
        for feeder in lv_feeders:
            region = feeder['inspection_data__transformer_data__basestation__region']
            csc = feeder['inspection_data__transformer_data__basestation__csc']
            unbalance = float(feeder['current_phase_unbalance'] or 0)

            if not region or not csc:
                continue  # Skip records with missing region or csc

            if region not in region_data:
                region_data[region] = {
                    'key': region.lower(),
                    'region': region,
                    'csc': '',
                    'balanced': 0,
                    'unbalanced': 0,
                    'children': []
                }

            csc_key = f"{region.lower()}-{csc.lower()}"
            csc_entry = next(
                (c for c in region_data[region]['children'] if c['key'] == csc_key),
                None
            )

            if not csc_entry:
                csc_entry = {
                    'key': csc_key,
                    'region': region,
                    'csc': csc,
                    'balanced': 0,
                    'unbalanced': 0
                }
                region_data[region]['children'].append(csc_entry)

            if unbalance <= 10:
                region_data[region]['balanced'] += 1
                csc_entry['balanced'] += 1
            else:
                region_data[region]['unbalanced'] += 1
                csc_entry['unbalanced'] += 1

    elif analysis_type == 'percentage_of_neutral':
        # Get LvFeeder data directly
        lv_feeders = (
            LvFeeder.objects
            .filter(inspection_data_id__in=[item['latest_id'] for item in latest_inspections])
            .select_related('inspection_data__transformer_data__basestation')
            .values(
                'inspection_data__transformer_data__basestation__region',
                'inspection_data__transformer_data__basestation__csc',
                'percentage_of_neutral'
            )
            .exclude(percentage_of_neutral__isnull=True)
        )

        region_data = {}
        for feeder in lv_feeders:
            region = feeder['inspection_data__transformer_data__basestation__region']
            csc = feeder['inspection_data__transformer_data__basestation__csc']
            neutral_percentage = float(feeder['percentage_of_neutral'] or 0)

            if not region or not csc:
                continue  # Skip records with missing region or csc

            # Initialize region if not exists
            if region not in region_data:
                region_data[region] = {
                    'key': region.lower(),
                    'region': region,
                    'csc': '',
                    'normal': 0,
                    'high': 0,
                    'children': []
                }

            # Initialize CSC entry
            csc_key = f"{region.lower()}-{csc.lower()}"
            csc_entry = next(
                (c for c in region_data[region]['children'] if c['key'] == csc_key),
                None
            )

            if not csc_entry:
                csc_entry = {
                    'key': csc_key,
                    'region': region,
                    'csc': csc,
                    'normal': 0,
                    'high': 0
                }
                region_data[region]['children'].append(csc_entry)

            # Update counters based on neutral percentage
            if neutral_percentage <= 20:
                region_data[region]['normal'] += 1
                csc_entry['normal'] += 1
            else:
                region_data[region]['high'] += 1
                csc_entry['high'] += 1

    # Calculate percentages for all analysis types
    result = []
    for region, data in region_data.items():
        # Calculate total for the region
        if analysis_type == 'transformer_load':
            total = (data['below20'] + data['load20_50'] + data['load50_80'] +
                    data['load80_100'] + data['above100'])
            if total > 0:
                data['below20Percentage'] = round((data['below20'] / total) * 100)
                data['load20_50Percentage'] = round((data['load20_50'] / total) * 100)
                data['load50_80Percentage'] = round((data['load50_80'] / total) * 100)
                data['load80_100Percentage'] = round((data['load80_100'] / total) * 100)
                data['above100Percentage'] = round((data['above100'] / total) * 100)

            # Calculate percentages for each CSC
            for child in data['children']:
                child_total = (child['below20'] + child['load20_50'] + child['load50_80'] +
                             child['load80_100'] + child['above100'])
                if child_total > 0:
                    child['below20Percentage'] = round((child['below20'] / child_total) * 100)
                    child['load20_50Percentage'] = round((child['load20_50'] / child_total) * 100)
                    child['load50_80Percentage'] = round((child['load50_80'] / child_total) * 100)
                    child['load80_100Percentage'] = round((child['load80_100'] / child_total) * 100)
                    child['above100Percentage'] = round((child['above100'] / child_total) * 100)

        elif analysis_type == 'current_phase_unbalance':
            total = data['balanced'] + data['unbalanced']
            if total > 0:
                data['balancedPercentage'] = round((data['balanced'] / total) * 100)
                data['unbalancedPercentage'] = round((data['unbalanced'] / total) * 100)

            for child in data['children']:
                child_total = child['balanced'] + child['unbalanced']
                if child_total > 0:
                    child['balancedPercentage'] = round((child['balanced'] / child_total) * 100)
                    child['unbalancedPercentage'] = round((child['unbalanced'] / child_total) * 100)

        else:  # percentage_of_neutral
            total = data['normal'] + data['high']
            if total > 0:
                data['normalPercentage'] = round((data['normal'] / total) * 100)
                data['highPercentage'] = round((data['high'] / total) * 100)

            for child in data['children']:
                child_total = child['normal'] + child['high']
                if child_total > 0:
                    child['normalPercentage'] = round((child['normal'] / child_total) * 100)
                    child['highPercentage'] = round((child['high'] / child_total) * 100)

        result.append(data)

    return JsonResponse(result, safe=False)



@api_view(['GET'])
@permission_classes([IsAuthenticated])
def general_stats(request):
    total_transformers = TransformerData.objects.count()
    total_inspections = Inspection.objects.count()
    total_basestations = Basestation.objects.count()
    total_regions = Basestation.objects.values('region').distinct().count()
    total_cscs = Basestation.objects.values('csc').distinct().count()
    total_substations = Basestation.objects.values('substation').distinct().count()
    total_feeders = Basestation.objects.values('feeder').distinct().count()
    # First, get the latest inspection IDs for each transformer
    latest_inspections = Inspection.objects.values('transformer_data').annotate(
        latest_id=Max('id')
    ).values_list('latest_id', flat=True)

    # Then, count LV feeders that belong to these latest inspections
    total_lv_feeders = LvFeeder.objects.filter(
        inspection_data_id__in=latest_inspections
    ).count()

    thirty_days_ago = datetime.now() - timedelta(days=30)
    recent_inspections = Inspection.objects.filter(
        created_at__gte=thirty_days_ago
    ).count()

    inspection_threshold = datetime.now() - timedelta(days=120)
    pending_inspections = TransformerData.objects.exclude(
        inspections_transformer_data__created_at__gte=inspection_threshold
    ).count()

    return Response({
        'total_transformers': total_transformers,
        'total_inspections': total_inspections,
        'total_basestations': total_basestations,
        'total_regions': total_regions,
        'total_cscs': total_cscs,
        'recent_inspections': recent_inspections,
        'pending_inspections': pending_inspections,
        'total_substations': total_substations,
        'total_feeders': total_feeders,
        'total_lv_feeders': total_lv_feeders
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def inspection_status(request):
    # Get date range from query parameters or use default (4 months)
    end_date = request.query_params.get('end_date')
    start_date = request.query_params.get('start_date')

    if not end_date:
        end_date = datetime.now().date()
    else:
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    if not start_date:
        start_date = end_date - timedelta(days=120)  # Default 4 months
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()

    # Get all inspected transformer IDs within the date range
    inspected_transformer_ids = set(
        Inspection.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).values_list('transformer_data_id', flat=True).distinct()
    )

    # Get all transformers with their region and CSC info in a single query
    transformers = TransformerData.objects.select_related('basestation').values(
        'id',
        'basestation__region',
        'basestation__csc'
    )

    # Initialize data structure
    region_stats = {}

    # Process all transformers in a single pass
    for transformer in transformers:
        region = transformer['basestation__region']
        csc = transformer['basestation__csc']

        # Skip if region is None
        if not region:
            continue

        # Initialize region if not exists
        if region not in region_stats:
            region_stats[region] = {
                'key': str(region).lower() if region else '',  # Safe conversion
                'region': region,
                'inspected': 0,
                'not_inspected': 0,
                'children': {},
            }

        # Skip if CSC is None
        if not csc:
            continue

        # Initialize CSC if not exists
        if csc not in region_stats[region]['children']:
            region_stats[region]['children'][csc] = {
                'key': f"{str(region).lower()}-{str(csc).lower()}" if region and csc else '',  # Safe conversion
                'region': region,
                'csc': csc,
                'inspected': 0,
                'not_inspected': 0,
            }

        # Update counts
        is_inspected = transformer['id'] in inspected_transformer_ids
        if is_inspected:
            region_stats[region]['inspected'] += 1
            region_stats[region]['children'][csc]['inspected'] += 1
        else:
            region_stats[region]['not_inspected'] += 1
            region_stats[region]['children'][csc]['not_inspected'] += 1

    # Convert to final format
    result = []
    for region_data in region_stats.values():
        region_data['children'] = list(region_data['children'].values())
        result.append(region_data)

    return Response(result)













@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_inspection_with_feeders(request):
    """
    Create an inspection record with associated LV feeders in a single transaction.
    """
    try:
        with transaction.atomic():
            # Extract LV feeders data from the request
            lvfeeders_data = request.data.pop('lvfeeders', [])

            # Create the inspection record
            inspection_data = request.data.copy()

            # Convert string values to appropriate types
            for field in ['N_load_current', 'R_S_Voltage', 'R_T_Voltage', 'T_S_Voltage']:
                if field in inspection_data and inspection_data[field] is not None:
                    try:
                        inspection_data[field] = Decimal(str(inspection_data[field]))
                    except (ValueError, TypeError):
                        pass

            # Create inspection instance
            inspection_serializer = InspectionSerializer(data=inspection_data)
            if inspection_serializer.is_valid():
                inspection = inspection_serializer.save(
                    created_by=request.user,
                    updated_by=request.user
                )

                # Calculate voltage-related fields for inspection
                RS = inspection.R_S_Voltage or 0
                RT = inspection.R_T_Voltage or 0
                ST = inspection.T_S_Voltage or 0

                if RS and RT and ST:
                    max_diff = max(abs(RS-RT), abs(RS-ST), abs(RT-ST))
                    avg_voltage = (RS + RT + ST) / 3
                    voltage_phase_unbalance = (max_diff / avg_voltage) * 100 if avg_voltage != 0 else 0
                    average_voltage = avg_voltage
                else:
                    voltage_phase_unbalance = 0
                    average_voltage = 0

                inspection.voltage_phase_unbalance = voltage_phase_unbalance
                inspection.average_voltage = average_voltage
                inspection.save()

                # Create LV feeder records
                created_feeders = []
                total_transformer_load = Decimal('0')

                for feeder_data in lvfeeders_data:
                    # Add inspection reference to feeder data
                    feeder_data['inspection_data'] = inspection.id

                    # Convert string values to appropriate types
                    for field in ['R_load_current', 'S_load_current', 'T_load_current']:
                        if field in feeder_data and feeder_data[field] is not None:
                            try:
                                feeder_data[field] = Decimal(str(feeder_data[field]))
                            except (ValueError, TypeError):
                                pass

                    # Create feeder instance
                    feeder_serializer = LvFeederSerializer(data=feeder_data)
                    if feeder_serializer.is_valid():
                        feeder = feeder_serializer.save(
                            created_by=request.user,
                            updated_by=request.user
                        )

                        # Calculate and update derived fields
                        try:
                            # Get load currents
                            R = Decimal(str(feeder.R_load_current or 0))
                            S = Decimal(str(feeder.S_load_current or 0))
                            T = Decimal(str(feeder.T_load_current or 0))
                            N = Decimal(str(inspection.N_load_current or 0))

                            # Get transformer capacity
                            transformer_data = inspection.transformer_data
                            capacity = Decimal(str(transformer_data.capacity)) if transformer_data and transformer_data.capacity else Decimal('0')

                            # Calculate Iconst (constant current)
                            Iconst = capacity / (Decimal('3') ** Decimal('0.5') * Decimal('0.4')) if capacity != 0 else Decimal('0')

                            # Calculate average current
                            Iavg = (R + S + T) / 3 if (R or S or T) else Decimal('0')

                            # Calculate metrics
                            transformer_load = (Iavg / Iconst) * 100 if Iconst != 0 else Decimal('0')
                            current_phase_unbalance = (max(abs(R-S), abs(R-T), abs(S-T)) / Iavg) * 100 if Iavg != 0 else Decimal('0')
                            percentage_of_neutral = (N / Iavg) * 100 if Iavg != 0 else Decimal('0')

                            # Update the feeder with calculated values
                            feeder.transformer_load = transformer_load
                            feeder.current_phase_unbalance = current_phase_unbalance
                            feeder.percentage_of_neutral = percentage_of_neutral
                            feeder.save()

                            # Add to total transformer load
                            total_transformer_load += transformer_load

                        except (ValueError, TypeError, ZeroDivisionError) as calc_error:
                            # If calculations fail, use default values
                            feeder.transformer_load = Decimal('0')
                            feeder.current_phase_unbalance = Decimal('0')
                            feeder.percentage_of_neutral = Decimal('0')
                            feeder.save()
                            print(f"Calculation warning: {calc_error}. Using default values.")

                        created_feeders.append(feeder_serializer.data)
                    else:
                        # If feeder validation fails, raise exception to trigger rollback
                        return Response(
                            {"error": "Invalid feeder data", "details": feeder_serializer.errors},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                # Update the inspection with total transformer load
                inspection.total_transformer_load = total_transformer_load
                inspection.save()

                # Log activity
                log_activity(
                    user=request.user,
                    action='CREATE',
                    model_name='Inspection',
                    record_id=inspection.id,
                    changes=inspection_data,
                    ip_address=request.META.get('REMOTE_ADDR')
                )

                # Return success response with created data
                return Response({
                    "message": "Inspection with feeders created successfully",
                    "inspection": inspection_serializer.data,
                    "feeders": created_feeders
                }, status=status.HTTP_201_CREATED)
            else:
                # If inspection validation fails, return error
                return Response(
                    {"error": "Invalid inspection data", "details": inspection_serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )

    except Exception as e:
        # Log error
        log_error(
            level='ERROR',
            message=f'create_inspection_with_feeders() failed: {type(e).__name__}: {str(e)}',
            user=request.user,
            traceback=str(e),
            ip_address=request.META.get('REMOTE_ADDR')
        )
        # Return error response
        return Response(
            {"error": "Failed to create inspection with feeders", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )




@api_view(['POST'])
@permission_classes([IsAuthenticated])
def process_transformer_offline(request):
    """
    Process offline transformer data collected from mobile devices
    and save to database as new records.
    """
    try:
        # Get the JSON data from the request
        data = request.data

        # Track processed items for response
        processed_items = []
        errors = []

        # Process each transformer in the batch
        with transaction.atomic():
            for item in data if isinstance(data, list) else [data]:
                try:
                    # Link to basestation if provided
                    basestation_code = item.get('basestation')
                    basestation = None
                    if basestation_code:
                        try:
                            basestation = Basestation.objects.get(station_code=basestation_code)
                        except Basestation.DoesNotExist:
                            # Log that basestation wasn't found
                            print(f"Basestation not found during offline sync: {basestation_code}")
                            return Response(
                                {"error": f"Basestation not found: {basestation_code}"},
                                status=status.HTTP_400_BAD_REQUEST
                            )

                    # Prepare transformer data
                    transformer_data = {
                        'dt_number': item.get('dt_number'),
                        'serial_number': item.get('serial_number'),
                        'manufacturer': item.get('manufacturer'),
                        'year_of_manufacturing': item.get('year_of_manufacturing') if item.get('year_of_manufacturing') != "" else None,
                        'date_of_installation': item.get('date_of_installation') if item.get('date_of_installation') != "" else None,
                        'capacity': item.get('capacity'),
                        'primary_voltage': item.get('primary_voltage'),
                        'trafo_type': item.get('trafo_type'),
                        'colling_type': item.get('colling_type'),
                        'vector_group': item.get('vector_group'),
                        'impedance_voltage': item.get('impedance_voltage'),
                        'oil_weight': item.get('oil_weight'),
                        'winding_weight': item.get('winding_weight'),
                        'service_type': item.get('service_type'),
                        'status': item.get('status'),
                        'basestation': basestation,  # Link basestation directly during creation
                    }

                    # Create new transformer
                    transformer = TransformerData.objects.create(
                        **transformer_data,
                        created_by=request.user,
                        updated_by=request.user
                    )

                    # Process inspection data if available
                    if 'inspection_data' in item and item['inspection_data']:
                        inspection_data = item['inspection_data']

                        # Convert voltage values to Decimal or None
                        RS = Decimal(str(inspection_data.get('R_S_Voltage'))) if inspection_data.get('R_S_Voltage') else None
                        RT = Decimal(str(inspection_data.get('R_T_Voltage'))) if inspection_data.get('R_T_Voltage') else None
                        ST = Decimal(str(inspection_data.get('T_S_Voltage'))) if inspection_data.get('T_S_Voltage') else None

                        # Calculate average voltage and voltage phase unbalance
                        if RS is not None and RT is not None and ST is not None:
                            max_diff = max(abs(RS-RT), abs(RS-ST), abs(RT-ST))
                            avg_voltage = (RS + RT + ST) / 3
                            voltage_phase_unbalance = (max_diff / avg_voltage) * 100 if avg_voltage != 0 else None
                            average_voltage = avg_voltage
                        else:
                            voltage_phase_unbalance = None
                            average_voltage = None

                        # Create inspection record
                        inspection = Inspection.objects.create(
                            transformer_data=transformer,
                            R_S_Voltage=inspection_data.get('R_S_Voltage'),
                            R_T_Voltage=inspection_data.get('R_T_Voltage'),
                            T_S_Voltage=inspection_data.get('T_S_Voltage'),
                            average_voltage=round(average_voltage, 2) if average_voltage is not None else None,
                            voltage_phase_unbalance=round(voltage_phase_unbalance, 2) if voltage_phase_unbalance is not None else None,
                            N_load_current=inspection_data.get('N_load_current'),
                            oil_level=inspection_data.get('oil_level'),
                            silica_gel=inspection_data.get('silica_gel'),
                            body_condition=inspection_data.get('body_condition'),
                            mounting_condition=inspection_data.get('mounting_condition'),
                            status_of_mounting=inspection_data.get('status_of_mounting'),
                            neutral_ground=inspection_data.get('neutral_ground'),
                            arrester_body_ground=inspection_data.get('arrester_body_ground'),
                            has_linkage=inspection_data.get('has_linkage'),
                            mv_bushing=inspection_data.get('mv_bushing'),
                            lv_bushing=inspection_data.get('lv_bushing'),
                            mv_cable_lug=inspection_data.get('mv_cable_lug'),
                            lv_cable_lug=inspection_data.get('lv_cable_lug'),
                            arrester=inspection_data.get('arrester'),
                            drop_out=inspection_data.get('drop_out'),
                            fuse_link=inspection_data.get('fuse_link'),
                            horn_gap=inspection_data.get('horn_gap'),
                            insulation_level=inspection_data.get('insulation_level'),
                            created_by=request.user,
                            updated_by=request.user,
                        )

                        # Initialize total transformer load
                        total_transformer_load = Decimal('0')

                        # Process LV feeders if available
                        if 'lvfeeders' in inspection_data and inspection_data['lvfeeders']:
                            for feeder_data in inspection_data['lvfeeders']:
                                # Calculate transformer load, current phase unbalance, and percentage of neutral
                                try:
                                    # Get current values, use None for missing values
                                    R = Decimal(str(feeder_data.get('R_load_current'))) if feeder_data.get('R_load_current') else None
                                    S = Decimal(str(feeder_data.get('S_load_current'))) if feeder_data.get('S_load_current') else None
                                    T = Decimal(str(feeder_data.get('T_load_current'))) if feeder_data.get('T_load_current') else None
                                    N = Decimal(str(inspection_data.get('N_load_current'))) if inspection_data.get('N_load_current') else None

                                    # Calculate average current if we have at least one phase current
                                    if R is not None or S is not None or T is not None:
                                        # Replace None with 0 for calculation purposes
                                        R_calc = R if R is not None else Decimal('0')
                                        S_calc = S if S is not None else Decimal('0')
                                        T_calc = T if T is not None else Decimal('0')
                                        Iavg = (R_calc + S_calc + T_calc) / 3
                                    else:
                                        Iavg = None

                                    # Calculate transformer capacity constant
                                    capacity = Decimal(str(transformer.capacity)) if transformer.capacity else None
                                    if capacity is not None and capacity != 0:
                                        Iconst = capacity / (Decimal('3') ** Decimal('0.5') * Decimal('0.4'))
                                    else:
                                        Iconst = None

                                    # Calculate metrics
                                    if Iavg is not None and Iconst is not None and Iconst != 0:
                                        transformer_load = (Iavg / Iconst) * 100
                                    else:
                                        transformer_load = None

                                    # Calculate current phase unbalance
                                    if Iavg is not None and Iavg > 0 and all(x is not None for x in [R, S, T]):
                                        max_deviation = max(abs(R-S), abs(R-T), abs(S-T))
                                        current_phase_unbalance = (max_deviation / Iavg) * 100
                                    else:
                                        current_phase_unbalance = None

                                    # Calculate percentage of neutral
                                    if N is not None and Iavg is not None and Iavg > 0:
                                        percentage_of_neutral = (N / Iavg) * 100
                                    else:
                                        percentage_of_neutral = None

                                    # Create LV feeder with calculated values
                                    lv_feeder = LvFeeder.objects.create(
                                        inspection_data=inspection,
                                        distribution_box_name=feeder_data.get('distribution_box_name'),
                                        R_load_current=feeder_data.get('R_load_current'),
                                        S_load_current=feeder_data.get('S_load_current'),
                                        T_load_current=feeder_data.get('T_load_current'),
                                        R_fuse_rating=feeder_data.get('R_fuse_rating'),
                                        S_fuse_rating=feeder_data.get('S_fuse_rating'),
                                        T_fuse_rating=feeder_data.get('T_fuse_rating'),
                                        transformer_load=round(transformer_load, 2) if transformer_load is not None else None,
                                        current_phase_unbalance=round(current_phase_unbalance, 2) if current_phase_unbalance is not None else None,
                                        percentage_of_neutral=round(percentage_of_neutral, 2) if percentage_of_neutral is not None else None,
                                        created_by=request.user,
                                        updated_by=request.user,
                                    )

                                    # Add to total transformer load if available
                                    if transformer_load is not None:
                                        total_transformer_load += transformer_load

                                except (ValueError, TypeError, ZeroDivisionError) as calc_error:
                                    # If calculations fail, create with null values
                                    LvFeeder.objects.create(
                                        inspection_data=inspection,
                                        distribution_box_name=feeder_data.get('distribution_box_name'),
                                        R_load_current=feeder_data.get('R_load_current'),
                                        S_load_current=feeder_data.get('S_load_current'),
                                        T_load_current=feeder_data.get('T_load_current'),
                                        R_fuse_rating=feeder_data.get('R_fuse_rating'),
                                        S_fuse_rating=feeder_data.get('S_fuse_rating'),
                                        T_fuse_rating=feeder_data.get('T_fuse_rating'),
                                        transformer_load=None,
                                        current_phase_unbalance=None,
                                        percentage_of_neutral=None,
                                        created_by=request.user,
                                        updated_by=request.user,
                                    )
                                    print(f"Calculation warning: {calc_error}. Using null values.")

                        # Update the inspection with total transformer load
                        inspection.total_transformer_load = total_transformer_load
                        inspection.save(update_fields=['total_transformer_load'])

                    # Log activity
                    log_activity(
                        user=request.user,
                        action='CREATE',
                        model_name='TransformerData',
                        record_id=transformer.id,
                        changes=transformer_data,
                        ip_address=request.META.get('REMOTE_ADDR')
                    )

                    # Add to processed items
                    processed_items.append({
                        'id': transformer.id,
                        'dt_number': transformer.dt_number,
                        'serial_number': transformer.serial_number,
                        'status': 'created'
                    })

                except Exception as item_error:
                    # Track errors but continue processing other items
                    errors.append({
                        'item_id': item.get('id', 'unknown'),
                        'error': str(item_error)
                    })

        # Return response with results
        return Response({
            'status': 'success',
            'message': f'Created {len(processed_items)} new transformer records',
            'processed_items': processed_items,
            'error': errors
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'status': 'error',
            'message': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)




@api_view(['POST'])
@permission_classes([IsAuthenticated])
def process_basestation_transformer_offline(request):
    """
    Process offline basestation transformer data collected from mobile devices
    and save to database as new records.
    """
    try:
        data = request.data
        processed_items = []
        errors = []

        items = data if isinstance(data, list) else [data]

        with transaction.atomic():
            for item in items:
                try:
                    # Validate required base fields
                    required_base_fields = ['region', 'csc', 'substation', 'feeder', 'address', 'gps_location', 'station_type']
                    missing_base_fields = [f for f in required_base_fields if item.get(f) is None]
                    if missing_base_fields:
                        raise ValueError(f"Missing required basestation fields: {missing_base_fields}")

                    csc = item.get('csc')
                    region = item.get('region')
                    substation = item.get('substation')
                    feeder = item.get('feeder')
                    address = item.get('address')
                    gps_location = item.get('gps_location')

                    print(f"Processing item: {item}")

                    # Check for duplicate
                    if Basestation.objects.filter(address=address, gps_location=gps_location).exists():
                        raise ValidationError({"detail": "A base station with the same address and GPS location already exists. offiline"})

                    # Fetch related model instances for foreign keys
                    from account.models import Region, CSCCenter, Substation, Feeder
                    region_obj = Region.objects.filter(name=region).first()
                    csc_obj = CSCCenter.objects.filter(name=csc, region=region_obj).first() if region_obj else None
                    substation_obj = Substation.objects.filter(name=substation, region=region_obj).first() if region_obj else None
                    feeder_obj = Feeder.objects.filter(feeder_name=feeder, substation__name=substation).first()

                    basestation_data = {k: item.get(k) for k in required_base_fields}
                    basestation_data['station_code'] = generate_station_code(csc, region)
                    basestation_data['regionId'] = region_obj
                    basestation_data['cscId'] = csc_obj
                    basestation_data['substationId'] = substation_obj
                    basestation_data['feederId'] = feeder_obj

                    basestation = Basestation.objects.create(
                        **basestation_data,
                        created_by=request.user,
                        updated_by=request.user
                    )

                    transformer_data = item.get('transformer_data')
                    if not isinstance(transformer_data, dict):
                        raise ValueError("Invalid or missing transformer_data")

                    transformer_fields = [
                        'trafo_type', 'capacity', 'dt_number', 'primary_voltage', 'colling_type',
                        'serial_number', 'manufacturer', 'service_type', 'status', 'vector_group',
                        'impedance_voltage', 'winding_weight', 'oil_weight', 'year_of_manufacturing',
                        'date_of_installation'
                    ]
                    transformer_kwargs = {
                        k: transformer_data.get(k)
                        for k in transformer_fields
                    }
                    transformer_kwargs['basestation'] = basestation

                    transformer = TransformerData.objects.create(
                        **transformer_kwargs,
                        created_by=request.user,
                        updated_by=request.user
                    )

                    inspection_data = transformer_data.get('inspection_data', {})
                    if not isinstance(inspection_data, dict):
                        raise ValueError("Invalid or missing inspection_data")

                    inspection_fields = [
                        'body_condition', 'arrester', 'drop_out', 'mv_bushing', 'mv_cable_lug',
                        'lv_bushing', 'lv_cable_lug', 'oil_level', 'insulation_level', 'fuse_link',
                        'horn_gap', 'silica_gel', 'has_linkage', 'arrester_body_ground', 'neutral_ground',
                        'status_of_mounting', 'mounting_condition', 'N_load_current', 'R_S_Voltage',
                        'R_T_Voltage', 'T_S_Voltage'
                    ]
                    inspection_kwargs = {
                        k: inspection_data.get(k)
                        for k in inspection_fields
                    }

                    inspection_kwargs['transformer_data'] = transformer

                    print("inspection_kwargs",inspection_kwargs)

                    # Calculate average_voltage and voltage_phase_unbalance
                    def safe_decimal(value, default=Decimal('0')):
                        try:
                            return Decimal(str(value))
                        except (InvalidOperation, TypeError):
                            return default

                    RS = safe_decimal(inspection_kwargs.get('R_S_Voltage'))
                    RT = safe_decimal(inspection_kwargs.get('R_T_Voltage'))
                    ST = safe_decimal(inspection_kwargs.get('T_S_Voltage'))

                    if RS != 0 and RT != 0 and ST != 0:
                        avg_voltage = (RS + RT + ST) / 3
                        max_diff = max(abs(RS - RT), abs(RS - ST), abs(RT - ST))
                        voltage_phase_unbalance = (max_diff / avg_voltage * Decimal('100')) if avg_voltage else None
                    else:
                        avg_voltage = None
                        voltage_phase_unbalance = None

                    inspection_kwargs['average_voltage'] = round(avg_voltage, 2) if avg_voltage else None
                    inspection_kwargs['voltage_phase_unbalance'] = round(voltage_phase_unbalance, 2) if voltage_phase_unbalance else None

                    print("inspection_kwargs ******************",inspection_kwargs)

                    inspection = Inspection.objects.create(
                        **inspection_kwargs,
                        created_by=request.user,
                        updated_by=request.user
                    )

                    feeders = inspection_data.get('lvfeeders', [])
                    total_transformer_load = Decimal('0')

                    for feeder_data in feeders:
                        feeder_fields = [
                            'distribution_box_name', 'R_load_current', 'S_load_current', 'T_load_current',
                            'R_fuse_rating', 'S_fuse_rating', 'T_fuse_rating'
                        ]

                        feeder_kwargs = {k: feeder_data.get(k) for k in feeder_fields}
                        feeder_kwargs['inspection_data'] = inspection

                        R = safe_decimal(feeder_kwargs.get('R_load_current'))
                        S = safe_decimal(feeder_kwargs.get('S_load_current'))
                        T = safe_decimal(feeder_kwargs.get('T_load_current'))
                        N = safe_decimal(inspection_kwargs.get('N_load_current'))

                        Iavg = (R + S + T) / 3 if R and S and T else None
                        capacity = safe_decimal(transformer.capacity)

                        Iconst = capacity / (Decimal('3') ** Decimal('0.5') * Decimal('0.4')) if capacity and capacity != 0 else None

                        transformer_load = (Iavg / Iconst * Decimal('100')) if Iavg and Iconst and Iconst != 0 else None
                        current_phase_unbalance = None
                        percentage_of_neutral = None

                        if Iavg and Iavg > 0 and R and S and T:
                            max_deviation = max(abs(R-S), abs(R-T), abs(S-T))
                            current_phase_unbalance = (max_deviation / Iavg * Decimal('100'))

                        if N and Iavg and Iavg > 0:
                            percentage_of_neutral = (N / Iavg * Decimal('100'))

                        feeder_kwargs['transformer_load'] = round(transformer_load, 2) if transformer_load else None
                        feeder_kwargs['current_phase_unbalance'] = round(current_phase_unbalance, 2) if current_phase_unbalance else None
                        feeder_kwargs['percentage_of_neutral'] = round(percentage_of_neutral, 2) if percentage_of_neutral else None

                        LvFeeder.objects.create(**feeder_kwargs, created_by=request.user, updated_by=request.user)
                        if transformer_load:
                            total_transformer_load += transformer_load

                    inspection.total_transformer_load = total_transformer_load
                    inspection.save(update_fields=['total_transformer_load'])

                    processed_items.append({
                        'basestation': basestation.station_code,
                        'transformer_id': transformer.id,
                        'inspection_id': inspection.id,
                        'status': 'created'
                    })

                except Exception as inner_error:
                    errors.append({
                        'error': str(inner_error),
                        'item': item
                    })
                    continue

        response_status = status.HTTP_201_CREATED if len(processed_items) > 0 else status.HTTP_400_BAD_REQUEST
        return Response({
            'status': len(errors) == 0 and 'success' or 'partial_success',
            'message': f'Processed {len(processed_items)} basestation(s)',
            'processed_items': processed_items,
            'errors': errors
        }, status=response_status)

    except Exception as e:
        return Response({
            'status': 'error',
            'message': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


def generate_station_code(csc_value, region_value):
        from account.models import CSCCenter, Region

        base_code = None
        try:
            # csc = CSCCenter.objects.get(name=csc_value)
            csc = CSCCenter.objects.get(name=csc_value, region_id=region_value)
            base_code = csc.csc_code
        except CSCCenter.DoesNotExist:
            try:
                region = Region.objects.get(name=region_value)
                base_code = region.csc_code
            except Region.DoesNotExist:
                base_code = "UNKNOWN"

        last_station = Basestation.objects.filter(
            station_code__startswith=f"{base_code}-"
        ).order_by('-station_code').first()

        if last_station:
            try:
                last_id = int(last_station.station_code.split('-')[-1])
                new_id = last_id + 1
            except ValueError:
                new_id = 1
        else:
            new_id = 1

        return f"{base_code}-{new_id:04d}"


def update_inspection_total_load(inspection_id):
    """
    Update the total_transformer_load field in the Inspection model
    by summing up all associated LvFeeder transformer_load values.
    """
    from django.db.models import Sum
    from decimal import Decimal

    try:
        # Get the inspection
        inspection = Inspection.objects.get(id=inspection_id)

        # Sum up all transformer_load values from associated LvFeeders
        total_load = LvFeeder.objects.filter(
            inspection_data_id=inspection_id
        ).aggregate(
            total=Sum('transformer_load')
        )['total'] or Decimal('0')

        # Update the inspection with the total load
        inspection.total_transformer_load = total_load
        inspection.save(update_fields=['total_transformer_load'])

        print(f"Updated inspection {inspection_id} with total transformer load: {total_load}")

    except Exception as e:
        print(f"Error updating total transformer load: {e}")




from math import radians, cos, sin, asin, sqrt, atan2, degrees

@api_view(['GET'])
# @permission_classes([IsAuthenticated])
def nearest_basestation(request):
    """
    Find the 10 nearest Basestations to a given GPS location.
    Example request: /api/transformer/nearest/GPSLocation=9.019614,38.7939317
    """
    gps_param = request.GET.get('GPSLocation') or request.GET.get('gps_location')
    if not gps_param:
        return Response({'error': 'GPSLocation parameter is required. Format: GPSLocation=lat,lon'}, status=400)

    try:
        lat_str, lon_str = gps_param.split(',')
        lat = float(lat_str.strip())
        lon = float(lon_str.strip())
    except Exception:
        return Response({'error': 'Invalid GPSLocation format. Use: lat,lon'}, status=400)

    def haversine(lat1, lon1, lat2, lon2):
        # Calculate the great circle distance between two points on the earth (in km)
        R = 6371  # Earth radius in kilometers
        dlat = radians(lat2 - lat1)
        dlon = radians(lon2 - lon1)
        a = sin(dlat/2)**2 + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        return R * c

    def calculate_bearing(lat1, lon1, lat2, lon2):
        # Calculate the bearing between two points
        dlon = radians(lon2 - lon1)
        lat1 = radians(lat1)
        lat2 = radians(lat2)
        x = sin(dlon) * cos(lat2)
        y = cos(lat1) * sin(lat2) - sin(lat1) * cos(lat2) * cos(dlon)
        bearing = atan2(x, y)
        bearing_deg = (degrees(bearing) + 360) % 360
        # Convert bearing to compass direction
        directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW', 'N']
        idx = int((bearing_deg + 22.5) // 45)
        return directions[idx], round(bearing_deg, 2)

    basestations = []
    for basestation in Basestation.objects.exclude(gps_location__isnull=True).exclude(gps_location=''):
        try:
            gps = basestation.gps_location
            if ',' in gps:
                bs_lat_str, bs_lon_str = gps.split(',')
                bs_lat = float(bs_lat_str.strip())
                bs_lon = float(bs_lon_str.strip())
                distance_km = haversine(lat, lon, bs_lat, bs_lon)
                distance_m = round(distance_km * 1000, 1)  # Convert to meters
                direction, degree = calculate_bearing(lat, lon, bs_lat, bs_lon)
                basestations.append((distance_m, direction, degree, basestation))
        except Exception:
            continue

    # Sort by distance and get the 10 nearest
    basestations.sort(key=lambda x: x[0])
    nearest_10 = basestations[:10]

    from .serializers import BasestationSerializer
    result = []
    for distance_m, direction, degree, basestation in nearest_10:
        serializer = BasestationSerializer(basestation)
        result.append({
            'basestation': serializer.data,
            'distance_m': distance_m,
            'direction': f"{direction} ({degree}°)"
        })

    if result:
        return Response({'nearest_basestations': result})
    else:
        return Response({'error': 'No valid basestation with GPS location found.'}, status=404)






@api_view(['GET'])
# @permission_classes([IsAuthenticated])
def export_transformer_data_excel(request):
    user_key = f"export_excel_lock_{request.user.id}"
    # Try to acquire a lock for this user for 5 minutes
    if safe_cache_get(user_key):
        return Response(
            {"error": "An export is already in progress for your account. Please wait."},
            status=429
        )
    safe_cache_set(user_key, True, timeout=300)  # 5 minutes

    try:
        import time
        wb = openpyxl.Workbook()
        ws_basestation = wb.active
        ws_basestation.title = "Basestation"
        ws_transformer = wb.create_sheet("TransformerData")
        ws_inspection = wb.create_sheet("Inspection")
        ws_lvfeeder = wb.create_sheet("LvFeeder")

        def write_queryset(ws, queryset, fields, model_name):
            ws.append(fields)
            count = queryset.count()
            batch_size = 1000
            written = 0
            for obj in queryset.iterator(chunk_size=batch_size):
                row = []
                for field in fields:
                    value = getattr(obj, field, "")
                    if field in ['created_at', 'updated_at'] and value:
                        value = value.strftime('%Y-%m-%d %H:%M:%S')
                    elif hasattr(value, '__str__') and not isinstance(value, (str, int, float)):
                        value = str(value)
                    row.append(value)
                ws.append(row)
                written += 1
                if written % batch_size == 0:
                    print(f"{model_name}: {written}/{count} rows written...")
            print(f"{model_name}: {written}/{count} rows written (done)")

            # Auto-size columns
            for col in ws.columns:
                max_length = 0
                column = col[0].column_letter
                for cell in col:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                ws.column_dimensions[column].width = max_length + 2

        from .models import Basestation, TransformerData, Inspection, LvFeeder

        basestation_fields = [f.name for f in Basestation._meta.fields]
        write_queryset(ws_basestation, Basestation.objects.all(), basestation_fields, "Basestation")

        transformer_fields = [f.name for f in TransformerData._meta.fields]
        write_queryset(ws_transformer, TransformerData.objects.all(), transformer_fields, "TransformerData")

        inspection_fields = [f.name for f in Inspection._meta.fields]
        write_queryset(ws_inspection, Inspection.objects.all(), inspection_fields, "Inspection")

        lvfeeder_fields = [f.name for f in LvFeeder._meta.fields]
        write_queryset(ws_lvfeeder, LvFeeder.objects.all(), lvfeeder_fields, "LvFeeder")

        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=transformer_data.xlsx'
        wb.save(response)
        return response
    finally:
        safe_cache_delete(user_key)





@api_view(['GET'])
# @permission_classes([IsAuthenticated])
def export_postgres_backup(request):
    user_key = f"export_pgbackup_lock_{request.user.id}"

    if safe_cache_get(user_key):
        return Response(
            {"error": "A backup is already in progress for your account. Please wait."},
            status=429
        )
    safe_cache_set(user_key, True, timeout=300)  # 5 minutes

    try:
        # === Config ====
        PG_BIN = r"C:\Program Files\PostgreSQL\17\bin\pg_dump.exe"
        DB_NAME = "trafo"
        DB_USER = "postgres"
        DB_HOST = "localhost"
        DB_PORT = "5432"
        DB_PASSWORD = "postgres@rg"

        # === Generate timestamped file ===
        now = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{DB_NAME}_{now}.backup"

        # Temporary backup directory (auto cleaned)
        temp_dir = tempfile.gettempdir()
        backup_path = os.path.join(temp_dir, filename)

        # === Run pg_dump ===
        env = os.environ.copy()
        env['PGPASSWORD'] = DB_PASSWORD

        result = subprocess.run([
            PG_BIN,
            "-U", DB_USER,
            "-h", DB_HOST,
            "-p", DB_PORT,
            "-F", "c",
            "-b",
            "-f", backup_path,
            DB_NAME
        ], env=env, capture_output=True, text=True)

        if result.returncode != 0:
            return Response({"error": "Backup failed", "details": result.stderr}, status=500)

        # === Return the backup file as a download ===
        response = FileResponse(
            open(backup_path, 'rb'),
            content_type='application/octet-stream'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response

    finally:
        safe_cache_delete(user_key)






@api_view(['GET'])
@permission_classes([IsAuthenticated])
def region_dashboard(request):
    # Get region parameter from request
    region = request.GET.get('region')

    if not region:
        return Response({'error': 'Region parameter is required'}, status=400)

    # Filter base queryset by region
    region_basestations = Basestation.objects.filter(region=region)

    # Get transformer IDs for this region using the correct field name
    region_transformer_ids = TransformerData.objects.filter(
        basestation__in=region_basestations
    ).values_list('id', flat=True)

    # Calculate region-specific stats
    total_transformers = TransformerData.objects.filter(
        id__in=region_transformer_ids
    ).count()

    total_inspections = Inspection.objects.filter(
        transformer_data_id__in=region_transformer_ids
    ).count()

    total_basestations = region_basestations.count()

    total_cscs = region_basestations.values('csc').distinct().count()

    total_substations = region_basestations.values('substation').distinct().count()

    total_feeders = region_basestations.values('feeder').distinct().count()

    # Get latest inspection IDs for transformers in this region
    latest_inspections = Inspection.objects.filter(
        transformer_data_id__in=region_transformer_ids
    ).values('transformer_data').annotate(
        latest_id=Max('id')
    ).values_list('latest_id', flat=True)

    # Count LV feeders for latest inspections in this region
    total_lv_feeders = LvFeeder.objects.filter(
        inspection_data_id__in=latest_inspections
    ).count()

    # Recent inspections (last 30 days) for this region
    thirty_days_ago = datetime.now() - timedelta(days=30)
    recent_inspections = Inspection.objects.filter(
        transformer_data_id__in=region_transformer_ids,
        created_at__gte=thirty_days_ago
    ).count()

    # Pending inspections (transformers not inspected in last 120 days) for this region
    inspection_threshold = datetime.now() - timedelta(days=120)
    pending_inspections = TransformerData.objects.filter(
        id__in=region_transformer_ids
    ).exclude(
        inspections_transformer_data__created_at__gte=inspection_threshold
    ).count()

    # Inspection Summary based on date range
    inspection_summary = {}
    created_date_range = request.GET.getlist('created_date_range[]')

    # Set default date range to last 4 months if not provided
    if len(created_date_range) != 2:
        end_date = timezone.now()
        start_date = end_date - timedelta(days=120)  # 4 months = ~120 days

        # Format dates for display
        default_start = start_date.strftime('%Y-%m-%d')
        default_end = end_date.strftime('%Y-%m-%d')

        created_date_range = [default_start, default_end]
        is_default_range = True
    else:
        is_default_range = False

    try:
        if is_default_range:
            # Use already calculated timezone-aware dates
            start_date_aware = start_date
            end_date_aware = end_date.replace(hour=23, minute=59, second=59)
        else:
            # Parse provided dates
            start_date_aware = timezone.make_aware(datetime.strptime(created_date_range[0], '%Y-%m-%d'))
            end_date_aware = timezone.make_aware(datetime.strptime(created_date_range[1], '%Y-%m-%d'))
            # Add one day to include full end date
            end_date_aware = end_date_aware.replace(hour=23, minute=59, second=59)

        # Get UNIQUE inspected transformers in the date range for this region (one-to-one)
        inspected_transformer_ids = Inspection.objects.filter(
            transformer_data_id__in=region_transformer_ids,
            created_at__range=[start_date_aware, end_date_aware]
        ).values('transformer_data_id').distinct()

        # Count unique inspected transformers (one transformer counted only once)
        inspected_count = inspected_transformer_ids.count()

        # Get the actual transformer IDs as a list for further use if needed
        inspected_transformer_ids_list = list(inspected_transformer_ids.values_list('transformer_data_id', flat=True))

        # Count not inspected transformers in date range (region transformers - inspected unique transformers)
        not_inspected_count = total_transformers - inspected_count

        # Total inspections in date range for this region (this can be more than unique transformers)
        total_inspections_in_range = Inspection.objects.filter(
            transformer_data_id__in=region_transformer_ids,
            created_at__range=[start_date_aware, end_date_aware]
        ).count()

        # Calculate average inspections per transformer
        # avg_inspections_per_transformer = round(
        #     (total_inspections_in_range / inspected_count), 2
        # ) if inspected_count > 0 else 0

        inspection_summary = {
            'date_range': {
                'start_date': created_date_range[0],
                'end_date': created_date_range[1],
                'is_default': is_default_range
            },
            'inspected_transformers': inspected_count,  # Unique transformers only
            'not_inspected_transformers': not_inspected_count,
            'total_inspections_in_range': total_inspections_in_range,  # Total inspection records
            # 'avg_inspections_per_transformer': avg_inspections_per_transformer,
            # 'inspection_rate': round((inspected_count / total_transformers * 100), 2) if total_transformers > 0 else 0
        }

    except ValueError as e:
        inspection_summary = {
            'error': 'Invalid date format. Use YYYY-MM-DD format.',
            'details': str(e)
        }

    return Response({
        'region': region,
        'total_transformers': total_transformers,
        'total_inspections': total_inspections,
        'total_basestations': total_basestations,
        'total_cscs': total_cscs,
        'recent_inspections': recent_inspections,
        'pending_inspections': pending_inspections,
        'total_substations': total_substations,
        'total_feeders': total_feeders,
        'total_lv_feeders': total_lv_feeders,
        'inspection_summary': inspection_summary
    })

