from rest_framework import serializers
from .models import Permission, Role, User, Region, CSCCenter, Feeder, Substation  # Add CSCCenter to imports
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate


class PermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Permission
        fields = [
            'id',  # Include id for read-only purposes
            'parentId',
            'label',
            'name',
            'icon',
            'type',
            'route',
            'component',
            'order',
            'hide',
            'status',
            'new_feature',
            'frame_src'
        ]
        extra_kwargs = {
            'id': {'read_only': True}  # Ensure id is read-only
        }



class RecursiveField(serializers.Serializer):
    """A recursive field for handling nested structures."""
    def to_representation(self, value):
        serializer = self.parent.parent.__class__(value, context=self.context)
        return serializer.data

class PermissionSerializerNested(serializers.ModelSerializer):
    children = RecursiveField(many=True, read_only=True)  # Recursive field for children
    parentId = serializers.IntegerField(source='parentId.id', allow_null=True, required=False)  # Changed to IntegerField

    class Meta:
        model = Permission
        fields = [
            'id',
            'parentId',
            'label',
            'name',
            'icon',
            'type',
            'route',
            'component',
            'order',
            'hide',
            'status',
            'new_feature',
            'frame_src',
            'children'
        ]

    def to_representation(self, instance):
        """
        Customize the representation to properly nest children.
        """
        data = super().to_representation(instance)

        # Ensure that children are always included, even if empty
        if 'children' not in data or not data['children']:
            data['children'] = []

        return data

    # def to_representation(self, instance):
    #     """Customize the representation of the type field."""
    #     data = super().to_representation(instance)
    #     # Convert type integer to PermissionType label
    #     if 'type' in data:
    #         data['type'] = self.get_permission_type_display(instance.type)
    #     return data

    # def get_permission_type_display(self, type_value):
    #     """Map type integer to PermissionType label."""
    #     permission_type_map = {
    #         0: "PermissionType.CATALOGUE",
    #         1: "PermissionType.MENU",
    #         # Add more mappings as needed
    #     }
    #     return permission_type_map.get(type_value, "Unknown")


class RoleSerializer(serializers.ModelSerializer):
    # permissions = PermissionSerializer(many=True, read_only=True)

    class Meta:
        model = Role
        fields = ['id', 'name', 'label', 'status', 'order', 'desc', 'permission']
        extra_kwargs = {
            'id': {'read_only': True}  # Ensure id is read-only
        }

class PopulatedRoleSerializer(serializers.ModelSerializer):
    permission = PermissionSerializerNested(many=True, read_only=True)

    class Meta:
        model = Role
        fields = ['id', 'name', 'label', 'status', 'order', 'desc', 'permission']

    def to_representation(self, instance):
        """Filter to only show top-level permissions in the role"""
        data = super().to_representation(instance)
        data['permission'] = [
            perm for perm in data['permission'] 
            if perm.get('parentId') is None
        ]
        return data

class UserSerializer(serializers.ModelSerializer):
    role = RoleSerializer(read_only=True)  # Serialize the role as a nested object
    role_id = serializers.PrimaryKeyRelatedField(
        queryset=Role.objects.all(),
        source='role',
        write_only=True,
        required=False
    )

    class Meta:
        model = User
        fields = [
            'id',
            'username',
            'email',
            'avatar',
            'phone',
            'full_name',
            'region', 
            'csc', 
            'title', 
            'address',
            'city',
            'about',
            'role',
            'role_id',  # For writing the role
            'is_staff',
            'is_active',
            'created_at',
            'updated_at'
        ]
        extra_kwargs = {
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True}
        }

    def create(self, validated_data):
        """Create a new user with a default password and role permissions."""
        default_password = "Eeu@1234"
        
        # Create the user first
        user = User.objects.create_user(
            username=validated_data['username'],
            email=validated_data['email'],
            password=default_password,
            avatar=validated_data.get('avatar', None),
            role=validated_data.get('role', None),
            is_staff=validated_data.get('is_staff', True),
            is_active=validated_data.get('is_active', True)
        )

        # # If role is assigned, copy its permissions to the user
        # if user.role:
        #     role_permissions = user.role.permission.all()
        #     user.permissions.set(role_permissions)
        #     user.save()

        return user

    def update(self, instance, validated_data):
        """Update the user instance without modifying the password."""
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance

# class UserSerializer(serializers.ModelSerializer):
#     role = RoleSerializer(read_only=True)
#     permissions = PermissionSerializer(many=True, read_only=True)

#     class Meta:
#         model = User
#         fields = ('id', 'username', 'email', 'avatar', 'role', 'permissions', 'password')
#         extra_kwargs = {'password': {'write_only': True}}

#     def create(self, validated_data):
#         # Hash the password before saving
#         password = validated_data.pop('password', None)
#         user = User.objects.create(**validated_data)
#         if password:
#             user.set_password(password)
#             user.save()
#         return user
    

# class LoginSerializer(serializers.Serializer):
#     email = serializers.EmailField()
#     password = serializers.CharField(write_only=True)

#     def validate(self, data):
#         email = data.get('email')
#         password = data.get('password')

#         if not email or not password:
#             raise serializers.ValidationError("Email and password are required.")

#         # Authenticate the user
#         user = authenticate(email=email, password=password)
#         if not user:
#             raise serializers.ValidationError("Invalid credentials.")

#         # Generate JWT tokens
#         refresh = RefreshToken.for_user(user)
#         data['user'] = UserSerializer(user).data
#         data['access_token'] = str(refresh.access_token)
#         data['refresh_token'] = str(refresh)

#         return data



class UserSerializer2(serializers.ModelSerializer):
    role = PopulatedRoleSerializer(read_only=True)

    class Meta:
        model = User
        fields = [
            'id',
            'username',
            'email',
            'avatar',
            'phone',
            'full_name',
            'region', 
            'csc', 
            'title', 
            'address',
            'city',
            'about',
            'role',
            'is_staff',
            'is_active',
            'created_at',
            'updated_at'
        ]
        extra_kwargs = {
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True}
        }

    # def to_representation(self, instance):
    #     """Filter to only show top-level permissions for the user"""
    #     data = super().to_representation(instance)
    #     data['permissions'] = [
    #         perm for perm in data['permissions'] 
    #         if perm.get('parentId') is None
    #     ]
    #     return data



class LoginSerializer(serializers.Serializer):
    username = serializers.CharField()  # Change email to username
    password = serializers.CharField(write_only=True)

    def validate(self, data):
        username = data.get('username')  # Use username instead of email
        password = data.get('password')

        if not username or not password:
            raise serializers.ValidationError("Username and password are required.")

        # Authenticate the user using username and password
        user = authenticate(username=username, password=password)  # Update authentication logic
        if not user:
            raise serializers.ValidationError("Invalid credentials.")

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        data['user'] = UserSerializer2(user).data
        data['accessToken'] = str(refresh.access_token)
        data['refreshToken'] = str(refresh)

        return data
    


from django.contrib.auth import get_user_model

User = get_user_model()

class RegisterSerializer(serializers.Serializer):
    email = serializers.EmailField()
    username = serializers.CharField(max_length=150)
    password = serializers.CharField(write_only=True, min_length=8)  # Ensure password has a minimum length
    role_id = serializers.CharField(required=False, allow_null=True)  # Optional role ID

    def validate(self, data):
        email = data.get('email')
        username = data.get('username')
        password = data.get('password')

        # Check if required fields are provided
        if not email or not username or not password:
            raise serializers.ValidationError("Email, username, and password are required.")

        # Check if the email is already registered
        if User.objects.filter(email=email).exists():
            raise serializers.ValidationError("A user with this email already exists.")

        # Optionally, check if the username is already taken
        if User.objects.filter(username=username).exists():
            raise serializers.ValidationError("A user with this username already exists.")

        return data

    def create(self, validated_data):
        # Extract validated data
        email = validated_data['email']
        username = validated_data['username']
        password = validated_data['password']
        role_id = validated_data.get('role_id', None)

        # Create the user
        user = User.objects.create_user(
            email=email,
            username=username,
            password=password,
            role_id=role_id  # Assign role if provided
        )

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)

        # Return serialized user data and tokens
        return {
            'user': UserSerializer(user).data,
            'access_token': str(refresh.access_token),
            'refresh_token': str(refresh),
        }
    


# class RegionSerializer(serializers.ModelSerializer):
#     children = serializers.SerializerMethodField()

#     class Meta:
#         model = Region
#         fields = ['key', 'name', 'parent', 'children']  # Include 'children' field

#     def get_children(self, obj):
#         """
#         Include 'children' only if they exist.
#         """
#         children = obj.children.all()
#         if children.exists():
#             return RegionSerializer(children, many=True).data
#         return None  # Return None instead of an empty list



class CSCSerializer(serializers.ModelSerializer):
    class Meta:
        model = CSCCenter
        fields = ['csc_code', 'name', 'region']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        request = self.context.get('request')
        if request and request.method in ['PUT', 'PATCH']:
            self.Meta.read_only_fields = ['csc_code', 'region']

class FeederSerializer(serializers.ModelSerializer):
    class Meta:
        model = Feeder
        fields = [
            'id',
            'feeder_name',
            'voltage_level',
            'peak_load',
            'length',
            'number_of_transformer',
            'substation'
        ]
        read_only_fields = ['id']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        request = self.context.get('request')
        if request and request.method in ['PUT', 'PATCH']:
            # Make substation read-only for updates
            self.Meta.read_only_fields = ['id', 'substation']

class SubstationSerializer(serializers.ModelSerializer):
    feeders = FeederSerializer(many=True, read_only=True)
    region_name = serializers.CharField(source='region.name', read_only=True)
    
    class Meta:
        model = Substation
        fields = ['id', 'name', 'region', 'region_name', 'feeders']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['region'] = instance.region.csc_code
        return representation

class RegionSerializer(serializers.ModelSerializer):
    csc_centers = CSCSerializer(many=True, read_only=True)
    substations = SubstationSerializer(many=True, read_only=True)

    class Meta:
        model = Region
        fields = ['csc_code', 'name', 'csc_centers', 'substations']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        request = self.context.get('request')
        if request and request.method in ['PUT', 'PATCH']:
            self.Meta.read_only_fields = ['csc_code']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['type'] = 'region'

        if representation.get('csc_centers'):
            for csc in representation['csc_centers']:
                csc['type'] = 'csc'

        if not representation['csc_centers']:
            representation.pop('csc_centers', None)

        return representation
    

class RegionOnlySerializer(serializers.ModelSerializer):
    class Meta:
        model = Region
        fields = ['csc_code', 'name']


class RegionSerializer1(serializers.ModelSerializer):

    class Meta:
        model = Region
        fields = ['name']

        




















