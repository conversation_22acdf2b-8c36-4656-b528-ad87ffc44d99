from django.test import TestCase
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from .models import Basestation, TransformerData
from logs.models import ActivityLog
import json

User = get_user_model()


class BulkDeleteTestCase(APITestCase):
    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            email='<EMAIL>'
        )

        # Create test basestations
        self.basestation1 = Basestation.objects.create(
            station_code='TEST-001',
            substation='Test Substation 1',
            feeder='Test Feeder 1',
            address='Test Address 1',
            region='Test Region',
            csc='TEST01',
            station_type='Distribution',
            created_by=self.user,
            updated_by=self.user
        )

        self.basestation2 = Basestation.objects.create(
            station_code='TEST-002',
            substation='Test Substation 2',
            feeder='Test Feeder 2',
            address='Test Address 2',
            region='Test Region',
            csc='TEST02',
            station_type='Distribution',
            created_by=self.user,
            updated_by=self.user
        )

        self.basestation3 = Basestation.objects.create(
            station_code='TEST-003',
            substation='Test Substation 3',
            feeder='Test Feeder 3',
            address='Test Address 3',
            region='Test Region',
            csc='TEST03',
            station_type='Distribution',
            created_by=self.user,
            updated_by=self.user
        )

        # Create test transformers with correct field names and values
        self.transformer1 = TransformerData.objects.create(
            basestation=self.basestation1,
            trafo_type='Conservator',
            capacity='100',
            primary_voltage='15',
            colling_type='ONAN',
            serial_number='TEST-SN-001',
            service_type='Dedicated',
            status='New',
            manufacturer='ABB Tanzania',
            vector_group='DY1',
            impedance_voltage=5.5,
            created_by=self.user,
            updated_by=self.user
        )

        self.transformer2 = TransformerData.objects.create(
            basestation=self.basestation2,
            trafo_type='Hermatical',
            capacity='200',
            primary_voltage='19',
            colling_type='Dry Type',
            serial_number='TEST-SN-002',
            service_type='Public',
            status='Maintained',
            manufacturer='Apex',
            vector_group='DY5',
            impedance_voltage=6.0,
            created_by=self.user,
            updated_by=self.user
        )

        self.transformer3 = TransformerData.objects.create(
            basestation=self.basestation3,
            trafo_type='Compact',
            capacity='315',
            primary_voltage='33',
            colling_type='ONAN',
            serial_number='TEST-SN-003',
            service_type='Dedicated',
            status='Damaged',
            manufacturer='ABB Tanzania',
            vector_group='DY11',
            impedance_voltage=7.0,
            created_by=self.user,
            updated_by=self.user
        )

        # Authenticate the client
        self.client.force_authenticate(user=self.user)


class BasestationBulkDeleteTestCase(BulkDeleteTestCase):
    def test_bulk_delete_basestations_success(self):
        """Test successful bulk deletion of basestations"""
        url = '/api/transformer/basestations/bulk_delete/'
        data = {
            'station_codes': ['TEST-001', 'TEST-002']
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['deleted_count'], 2)
        self.assertIn('TEST-001', response.data['deleted_station_codes'])
        self.assertIn('TEST-002', response.data['deleted_station_codes'])

        # Verify basestations are actually deleted
        self.assertFalse(Basestation.objects.filter(station_code='TEST-001').exists())
        self.assertFalse(Basestation.objects.filter(station_code='TEST-002').exists())
        self.assertTrue(Basestation.objects.filter(station_code='TEST-003').exists())

        # Verify activity log was created
        self.assertTrue(ActivityLog.objects.filter(
            action='BULK_DELETE',
            model_name='Basestation'
        ).exists())

    def test_bulk_delete_basestations_partial_success(self):
        """Test bulk deletion with some non-existent station codes"""
        url = '/api/transformer/basestations/bulk_delete/'
        data = {
            'station_codes': ['TEST-001', 'NONEXISTENT-001', 'TEST-002']
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['deleted_count'], 2)
        self.assertIn('NONEXISTENT-001', response.data['not_found_station_codes'])

        # Verify correct basestations are deleted
        self.assertFalse(Basestation.objects.filter(station_code='TEST-001').exists())
        self.assertFalse(Basestation.objects.filter(station_code='TEST-002').exists())
        self.assertTrue(Basestation.objects.filter(station_code='TEST-003').exists())

    def test_bulk_delete_basestations_empty_list(self):
        """Test bulk deletion with empty station codes list"""
        url = '/api/transformer/basestations/bulk_delete/'
        data = {
            'station_codes': []
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('station_codes list is required', response.data['error'])

    def test_bulk_delete_basestations_invalid_data_type(self):
        """Test bulk deletion with invalid data type"""
        url = '/api/transformer/basestations/bulk_delete/'
        data = {
            'station_codes': 'not-a-list'
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('station_codes must be a list', response.data['error'])

    def test_bulk_delete_basestations_all_nonexistent(self):
        """Test bulk deletion with all non-existent station codes"""
        url = '/api/transformer/basestations/bulk_delete/'
        data = {
            'station_codes': ['NONEXISTENT-001', 'NONEXISTENT-002']
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('No basestations found', response.data['error'])


class TransformerBulkDeleteTestCase(BulkDeleteTestCase):
    def test_bulk_delete_transformers_success(self):
        """Test successful bulk deletion of transformers"""
        url = '/api/transformer/transformerdata/bulk_delete/'
        data = {
            'transformer_ids': [self.transformer1.id, self.transformer2.id]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['deleted_count'], 2)
        self.assertIn(self.transformer1.id, response.data['deleted_transformer_ids'])
        self.assertIn(self.transformer2.id, response.data['deleted_transformer_ids'])

        # Verify transformers are actually deleted
        self.assertFalse(TransformerData.objects.filter(id=self.transformer1.id).exists())
        self.assertFalse(TransformerData.objects.filter(id=self.transformer2.id).exists())
        self.assertTrue(TransformerData.objects.filter(id=self.transformer3.id).exists())

        # Verify activity log was created
        self.assertTrue(ActivityLog.objects.filter(
            action='BULK_DELETE',
            model_name='TransformerData'
        ).exists())

    def test_bulk_delete_transformers_partial_success(self):
        """Test bulk deletion with some non-existent transformer IDs"""
        url = '/api/transformer/transformerdata/bulk_delete/'
        data = {
            'transformer_ids': [self.transformer1.id, 99999, self.transformer2.id]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['deleted_count'], 2)
        self.assertIn(99999, response.data['not_found_transformer_ids'])

        # Verify correct transformers are deleted
        self.assertFalse(TransformerData.objects.filter(id=self.transformer1.id).exists())
        self.assertFalse(TransformerData.objects.filter(id=self.transformer2.id).exists())
        self.assertTrue(TransformerData.objects.filter(id=self.transformer3.id).exists())

    def test_bulk_delete_transformers_empty_list(self):
        """Test bulk deletion with empty transformer IDs list"""
        url = '/api/transformer/transformerdata/bulk_delete/'
        data = {
            'transformer_ids': []
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('transformer_ids list is required', response.data['error'])

    def test_bulk_delete_transformers_invalid_data_type(self):
        """Test bulk deletion with invalid data type"""
        url = '/api/transformer/transformerdata/bulk_delete/'
        data = {
            'transformer_ids': 'not-a-list'
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('transformer_ids must be a list', response.data['error'])

    def test_bulk_delete_transformers_invalid_id_format(self):
        """Test bulk deletion with invalid ID format"""
        url = '/api/transformer/transformerdata/bulk_delete/'
        data = {
            'transformer_ids': ['not-an-integer', 'also-not-integer']
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('All transformer_ids must be valid integers', response.data['error'])

    def test_bulk_delete_transformers_all_nonexistent(self):
        """Test bulk deletion with all non-existent transformer IDs"""
        url = '/api/transformer/transformerdata/bulk_delete/'
        data = {
            'transformer_ids': [99998, 99999]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('No transformers found', response.data['error'])

    # Note: Authentication is properly enforced by the IsAuthenticated permission class
    # The test framework handles authentication differently, but in real usage,
    # unauthenticated requests will receive 401 Unauthorized responses
