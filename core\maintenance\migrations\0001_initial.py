# Generated by Django 5.1.6 on 2025-07-16 13:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Maintenance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True)),
                ('details', models.TextField()),
                ('asset', models.CharField(max_length=255)),
                ('parent_asset', models.CharField(blank=True, max_length=255, null=True)),
                ('status', models.CharField(choices=[('New', 'New'), ('Assigned', 'Assigned'), ('In Progress', 'In Progress'), ('Waiting For Parts', 'Waiting For Parts'), ('Complete', 'Complete')], default='New', max_length=20)),
                ('priority', models.CharField(choices=[('Low', 'Low'), ('Medium', 'Medium'), ('High', 'High'), ('Critical', 'Critical')], default='Low', max_length=20)),
                ('type', models.CharField(choices=[('Breakdown', 'Breakdown'), ('Corrective', 'Corrective'), ('Inspection', 'Inspection'), ('Preventive', 'Preventive'), ('Movement', 'Movement')], default='Preventive', max_length=20)),
                ('date_created', models.DateField(auto_now_add=True)),
                ('date_scheduled', models.DateField(blank=True, null=True)),
                ('date_due', models.DateField()),
                ('date_completed', models.DateField(blank=True, null=True)),
                ('assigned_to', models.ManyToManyField(blank=True, related_name='assigned_maintenances', to=settings.AUTH_USER_MODEL)),
                ('completed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_maintenances', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_maintenances', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-date_created'],
            },
        ),
        migrations.CreateModel(
            name='MaintenanceLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('change_type', models.CharField(choices=[('STATUS', 'Status Change'), ('ASSIGNMENT', 'Assignment Change'), ('PRIORITY', 'Priority Change'), ('OTHER', 'Other Change')], max_length=20)),
                ('old_value', models.TextField(blank=True, null=True)),
                ('new_value', models.TextField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('description', models.TextField()),
                ('maintenance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='maintenance.maintenance')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='MaintenanceUpdate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('maintenance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='updates', to='maintenance.maintenance')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='MaintenanceImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='maintenance_updates/')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('update', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='maintenance.maintenanceupdate')),
            ],
            options={
                'ordering': ['uploaded_at'],
            },
        ),
    ]
