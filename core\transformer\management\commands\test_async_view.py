from django.core.management.base import BaseCommand
from django.test import Async<PERSON><PERSON>
from django.contrib.auth.models import User
import asyncio
import time
import json

class Command(BaseCommand):
    help = 'Test AsyncBasestationsFilteredAPIView under concurrent load'

    def add_arguments(self, parser):
        parser.add_argument(
            '--requests',
            type=int,
            default=10,
            help='Number of concurrent requests to make (default: 10)'
        )
        parser.add_argument(
            '--page-size',
            type=int,
            default=50,
            help='Page size for requests (default: 50)'
        )

    def handle(self, *args, **options):
        num_requests = options['requests']
        page_size = options['page_size']
        
        self.stdout.write(
            self.style.SUCCESS(f'Testing AsyncBasestationsFilteredAPIView with {num_requests} concurrent requests')
        )
        
        # Run the async test
        results = asyncio.run(self.run_async_test(num_requests, page_size))
        
        # Display results
        self.display_results(results)

    async def run_async_test(self, num_requests, page_size):
        """Run concurrent async requests"""
        client = AsyncClient()
        
        # Test parameters
        test_params = [
            {"searchType": "BaseStation", "pageSize": str(page_size)},
            {"searchType": "Transformer", "pageSize": str(page_size)},
            {"searchType": "BaseStation", "region": "North", "pageSize": str(page_size // 2)},
            {"searchType": "Transformer", "without_base_station": "true", "pageSize": str(page_size // 3)},
        ]
        
        # Create tasks
        tasks = []
        for i in range(num_requests):
            params = test_params[i % len(test_params)]
            task = self.make_async_request(client, params, i + 1)
            tasks.append(task)
        
        # Run all requests concurrently
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        total_time = end_time - start_time
        
        # Process results
        successful = [r for r in results if isinstance(r, dict) and r.get('status') == 'success']
        failed = [r for r in results if isinstance(r, dict) and r.get('status') != 'success']
        exceptions = [r for r in results if not isinstance(r, dict)]
        
        return {
            'total_requests': num_requests,
            'successful': len(successful),
            'failed': len(failed),
            'exceptions': len(exceptions),
            'total_time': total_time,
            'successful_results': successful,
            'failed_results': failed,
            'exception_results': exceptions
        }

    async def make_async_request(self, client, params, request_id):
        """Make a single async request"""
        try:
            start_time = time.time()
            response = await client.get('/api/transformer/basestationsFilteredAsync/', params)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'request_id': request_id,
                    'status': 'success',
                    'response_time': response_time,
                    'status_code': response.status_code,
                    'data_count': len(data.get('results', [])) if isinstance(data, dict) else len(data),
                    'async_processed': data.get('async_processed', False) if isinstance(data, dict) else False
                }
            else:
                return {
                    'request_id': request_id,
                    'status': 'error',
                    'response_time': response_time,
                    'status_code': response.status_code,
                    'error': response.content.decode()[:200]
                }
        except Exception as e:
            return {
                'request_id': request_id,
                'status': 'exception',
                'response_time': 0,
                'error': str(e)
            }

    def display_results(self, results):
        """Display test results"""
        self.stdout.write("\n" + "="*50)
        self.stdout.write(self.style.SUCCESS("TEST RESULTS"))
        self.stdout.write("="*50)
        
        self.stdout.write(f"Total requests: {results['total_requests']}")
        self.stdout.write(f"Successful: {results['successful']}")
        self.stdout.write(f"Failed: {results['failed']}")
        self.stdout.write(f"Exceptions: {results['exceptions']}")
        self.stdout.write(f"Total time: {results['total_time']:.2f} seconds")
        
        if results['successful'] > 0:
            successful_results = results['successful_results']
            avg_time = sum(r['response_time'] for r in successful_results) / len(successful_results)
            max_time = max(r['response_time'] for r in successful_results)
            min_time = min(r['response_time'] for r in successful_results)
            
            self.stdout.write(f"\nResponse times:")
            self.stdout.write(f"  Average: {avg_time:.2f}s")
            self.stdout.write(f"  Max: {max_time:.2f}s")
            self.stdout.write(f"  Min: {min_time:.2f}s")
            
            async_count = sum(1 for r in successful_results if r.get('async_processed'))
            self.stdout.write(f"  Async processed: {async_count}/{len(successful_results)}")
        
        # Calculate success rate
        success_rate = (results['successful'] / results['total_requests']) * 100
        
        if success_rate >= 95:
            self.stdout.write(self.style.SUCCESS(f"\n✅ PASS: {success_rate:.1f}% success rate - Async implementation working well!"))
        elif success_rate >= 80:
            self.stdout.write(self.style.WARNING(f"\n⚠️  WARNING: {success_rate:.1f}% success rate - Some issues detected"))
        else:
            self.stdout.write(self.style.ERROR(f"\n❌ FAIL: {success_rate:.1f}% success rate - Significant issues detected"))
        
        # Show sample failures
        if results['failed']:
            self.stdout.write(f"\nSample failures:")
            for failure in results['failed_results'][:3]:
                self.stdout.write(f"  Request {failure['request_id']}: {failure.get('error', 'Unknown error')[:100]}")
        
        if results['exceptions']:
            self.stdout.write(f"\nSample exceptions:")
            for i, exc in enumerate(results['exception_results'][:3]):
                self.stdout.write(f"  Exception {i+1}: {str(exc)[:100]}")
