from django.core.management.base import BaseCommand
from transformer.models import Basestation, TransformerData, Inspection, LvFeeder
from datetime import datetime

class Command(BaseCommand):
    help = 'Delete records created before 5/23/2025 and clean up migrations'

    def handle(self, *args, **kwargs):
        # Define the cutoff date (May 23, 2025)
        cutoff = datetime(2025, 5, 23)

        self.stdout.write("Deleting LvFeeder records before 5/23/2025...")
        LvFeeder.objects.filter(created_at__lt=cutoff).delete()

        self.stdout.write("Deleting Inspection records before 5/23/2025...")
        Inspection.objects.filter(created_at__lt=cutoff).delete()

        self.stdout.write("Deleting TransformerData records before 5/23/2025...")
        TransformerData.objects.filter(created_at__lt=cutoff).delete()

        self.stdout.write("Deleting Basestation records before 5/23/2025...")
        Basestation.objects.filter(created_at__lt=cutoff).delete()

        self.stdout.write(self.style.SUCCESS("Successfully deleted records created before 5/23/2025 from transformer models"))