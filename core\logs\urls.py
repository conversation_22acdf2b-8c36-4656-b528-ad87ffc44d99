from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    DataChangeLogViewSet,
    AsyncActivityLogViewSet,
    AsyncErrorLogViewSet,
    AsyncDataChangeLogViewSet
)

router = DefaultRouter()
# Synchronous ViewSets (original)
router.register(r'changes', DataChangeLogViewSet, basename='changes')

# Async ViewSets for high concurrency (production-ready)
router.register(r'async/activities', AsyncActivityLogViewSet, basename='async_activities')
router.register(r'async/errors', AsyncErrorLogViewSet, basename='async_errors')
router.register(r'async/changes', AsyncDataChangeLogViewSet, basename='async_changes')

urlpatterns = [
    path('', include(router.urls)),
]
