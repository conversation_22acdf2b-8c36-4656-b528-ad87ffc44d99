# Generated by Django 5.1.6 on 2025-07-16 13:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('account', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Basestation',
            fields=[
                ('station_code', models.CharField(max_length=40, primary_key=True, serialize=False, unique=True)),
                ('region', models.Char<PERSON><PERSON>(max_length=125)),
                ('csc', models.Char<PERSON><PERSON>(max_length=125)),
                ('substation', models.Char<PERSON>ield(max_length=125)),
                ('feeder', models.Char<PERSON>ield(max_length=125)),
                ('station_type', models.CharField(blank=True, choices=[('Single Wooden Pole', 'Single Wooden Pole'), ('Single Concrete Pole', 'Single Concrete Pole'), ('Single Steel Pole', 'Single Steel Pole'), ('Double Wooden Pole', 'Double Wooden Pole'), ('Double Concrete Pole', 'Double Concrete Pole'), ('Double Steel Pole', 'Double Steel Pole'), ('Triple Wooden Pole', 'Triple Wooden Pole'), ('Triple Concrete Pole', 'Triple Concrete Pole'), ('Triple Steel Pole', 'Triple Steel Pole'), ('Quadruple Wooden Pole', 'Quadruple Wooden Pole'), ('Quadruple Concrete Pole', 'Quadruple Concrete Pole'), ('Quadruple Steel Pole', 'Quadruple Steel Pole'), ('Ground Seat Foundation Elevated', 'Ground Seat Foundation Elevated'), ('Ground Seat Foundation Ground Level', 'Ground Seat Foundation Ground Level'), ('Net Station', 'Net Station')], max_length=50, null=True)),
                ('address', models.CharField(max_length=125)),
                ('gps_location', models.CharField(max_length=125)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='Basestation_created_by', to=settings.AUTH_USER_MODEL)),
                ('cscId', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='Basestation_csc', to='account.csccenter')),
                ('feederId', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='Basestation_feeder', to='account.feeder')),
                ('regionId', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='Basestation_region', to='account.region')),
                ('substationId', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='Basestation_substation', to='account.substation')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='Basestation_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='Inspection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('body_condition', models.CharField(choices=[('Good', 'Good'), ('Fair', 'Fair'), ('Poor', 'Poor')], max_length=50, null=True)),
                ('arrester', models.CharField(choices=[('Ok', 'Ok'), ('one missed', 'One Missed'), ('two missed', 'Two Missed'), ('all missed', 'All Missed')], max_length=50, null=True)),
                ('drop_out', models.CharField(choices=[('Ok', 'Ok'), ('one missed', 'One Missed'), ('two missed', 'Two Missed'), ('all missed', 'All Missed')], max_length=50, null=True)),
                ('fuse_link', models.CharField(choices=[('Ok', 'Ok'), ('one missed', 'One Missed'), ('two missed', 'Two Missed'), ('all missed', 'All Missed')], max_length=50, null=True)),
                ('mv_bushing', models.CharField(choices=[('Ok', 'Ok'), ('one missed', 'One Missed'), ('two missed', 'Two Missed'), ('all missed', 'All Missed')], max_length=50, null=True)),
                ('mv_cable_lug', models.CharField(choices=[('Ok', 'Ok'), ('one missed', 'One Missed'), ('two missed', 'Two Missed'), ('all missed', 'All Missed')], max_length=50, null=True)),
                ('lv_bushing', models.CharField(choices=[('Ok', 'Ok'), ('one missed', 'One Missed'), ('two missed', 'Two Missed'), ('all missed', 'All Missed')], max_length=50, null=True)),
                ('lv_cable_lug', models.CharField(choices=[('Ok', 'Ok'), ('one missed', 'One Missed'), ('two missed', 'Two Missed'), ('all missed', 'All Missed')], max_length=50, null=True)),
                ('oil_level', models.CharField(choices=[('Full', 'Full'), ('0.75', '0.75'), ('0.5', '0.5'), ('0.25', '0.25')], max_length=50, null=True)),
                ('insulation_level', models.CharField(choices=[('Acceptable', 'Acceptable'), ('Not Acceptable', 'Not Acceptable')], max_length=50, null=True)),
                ('horn_gap', models.CharField(choices=[('Good', 'Good'), ('Poor', 'Poor')], max_length=50, null=True)),
                ('silica_gel', models.CharField(choices=[('Good', 'Good'), ('Fair', 'Fair'), ('Poor', 'Poor')], max_length=50, null=True)),
                ('has_linkage', models.CharField(choices=[('Yes', 'Yes'), ('No', 'No')], max_length=50, null=True)),
                ('arrester_body_ground', models.CharField(choices=[('Available', 'Available'), ('Not Available', 'Not Available')], max_length=50, null=True)),
                ('neutral_ground', models.CharField(choices=[('Available', 'Available'), ('Not Available', 'Not Available')], max_length=50, null=True)),
                ('status_of_mounting', models.CharField(choices=[('Good', 'Good'), ('Fair', 'Fair'), ('Poor', 'Poor')], max_length=50, null=True)),
                ('mounting_condition', models.CharField(choices=[('Good', 'Good'), ('Fair', 'Fair'), ('Poor', 'Poor')], max_length=50, null=True)),
                ('N_load_current', models.DecimalField(decimal_places=2, max_digits=10)),
                ('R_S_Voltage', models.DecimalField(decimal_places=2, max_digits=10)),
                ('R_T_Voltage', models.DecimalField(decimal_places=2, max_digits=10)),
                ('T_S_Voltage', models.DecimalField(decimal_places=2, max_digits=10)),
                ('voltage_phase_unbalance', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('average_voltage', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('total_transformer_load', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Total Transformer Load')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inspections_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inspections_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='LvFeeder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('distribution_box_name', models.CharField(max_length=125, verbose_name='Distribution Box Name')),
                ('R_load_current', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='R Load Current (A)')),
                ('S_load_current', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='S Load Current (A)')),
                ('T_load_current', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='T Load Current (A)')),
                ('R_fuse_rating', models.CharField(choices=[('63', '63 A'), ('80', '80  A'), ('100', '100 A'), ('150', '150 A'), ('160', '160 A'), ('200', '200 A'), ('250', '250 A'), ('300', '300 A'), ('315', '315 A'), ('350', '350 A'), ('400', '400 A'), ('500', '500 A'), ('600', '600 A'), ('Direct', 'Direct'), ('other', 'Other')], max_length=10, verbose_name='R Fuse Rating')),
                ('S_fuse_rating', models.CharField(choices=[('63', '63 A'), ('80', '80  A'), ('100', '100 A'), ('150', '150 A'), ('160', '160 A'), ('200', '200 A'), ('250', '250 A'), ('300', '300 A'), ('315', '315 A'), ('350', '350 A'), ('400', '400 A'), ('500', '500 A'), ('600', '600 A'), ('Direct', 'Direct'), ('other', 'Other')], max_length=10, verbose_name='S Fuse Rating')),
                ('T_fuse_rating', models.CharField(choices=[('63', '63 A'), ('80', '80  A'), ('100', '100 A'), ('150', '150 A'), ('160', '160 A'), ('200', '200 A'), ('250', '250 A'), ('300', '300 A'), ('315', '315 A'), ('350', '350 A'), ('400', '400 A'), ('500', '500 A'), ('600', '600 A'), ('Direct', 'Direct'), ('other', 'Other')], max_length=10, verbose_name='T Fuse Rating')),
                ('transformer_load', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='Transformer Load')),
                ('current_phase_unbalance', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='Current Phase Unbalance')),
                ('percentage_of_neutral', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='Percentage of Neutral Load')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='LvFeeder_created_by', to=settings.AUTH_USER_MODEL)),
                ('inspection_data', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='LvFeeder_inspection_data', to='transformer.inspection')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='LvFeeder_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='TransformerData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trafo_type', models.CharField(choices=[('Conservator', 'Conservator'), ('Hermatical', 'Hermatical'), ('Compact', 'Compact')], max_length=50, verbose_name='Transformer Type')),
                ('capacity', models.CharField(max_length=10, verbose_name='Capacity')),
                ('dt_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='DT Number')),
                ('primary_voltage', models.CharField(choices=[('15', '15 kVA'), ('19', '19 kVA'), ('33', '33 kVA'), ('null', 'Other')], max_length=10, verbose_name='Primary Voltage')),
                ('colling_type', models.CharField(choices=[('ONAN', 'ONAN'), ('Dry Type', 'Dry Type')], max_length=50, verbose_name='Cooling Type')),
                ('serial_number', models.CharField(max_length=50, unique=True, verbose_name='Serial Number')),
                ('service_type', models.CharField(choices=[('Dedicated', 'Dedicated'), ('Public', 'Public')], max_length=50, null=True, verbose_name='Service Type')),
                ('status', models.CharField(choices=[('New', 'New'), ('Maintained', 'Maintained'), ('Damaged', 'Damaged')], max_length=50, null=True, verbose_name='Status')),
                ('manufacturer', models.CharField(blank=True, choices=[('ABB Tanzania', 'ABB Tanzania'), ('Apex', 'Apex'), ('China Natinal Electric wire and cable Imp/Exp corporations', 'China Natinal Electric wire and cable Imp/Exp corporations'), ('Iran Transformer', 'Iran Transformer'), ('Kobera', 'Kobera'), ('Koncar', 'Koncar'), ("Mar son's", "Mar son's"), ('METEC', 'METEC'), ('Minel Transformer', 'Minel Transformer'), ('Pauwels', 'Pauwels'), ('Stromberg', 'Stromberg'), ('Vijai Electrical Ltd Hyderabad', 'Vijai Electrical Ltd Hyderabad'), ('Zennaro', 'Zennaro'), ('Other', 'Other')], max_length=100, null=True, verbose_name='Manufacturer')),
                ('vector_group', models.CharField(choices=[('DY1', 'DY1'), ('DY5', 'DY5'), ('DY11', 'DY11'), ('Other', 'Other')], max_length=10, verbose_name='Vector Group')),
                ('impedance_voltage', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='Impedance Voltage (%)')),
                ('winding_weight', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='Winding Weight (kg)')),
                ('oil_weight', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='Oil Weight (kg)')),
                ('year_of_manufacturing', models.IntegerField(blank=True, null=True, verbose_name='Year of Manufacturing')),
                ('date_of_installation', models.DateField(blank=True, null=True, verbose_name='Date of Installation')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('basestation', models.OneToOneField(blank=True, db_column='basestation_station_code', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transformer_data_basestation', to='transformer.basestation')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='TransformerData_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='TransformerData_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.AddField(
            model_name='inspection',
            name='transformer_data',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inspections_transformer_data', to='transformer.transformerdata'),
        ),
    ]
