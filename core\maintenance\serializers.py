from rest_framework import serializers
from .models import Maintenance, MaintenanceUpdate, MaintenanceImage, MaintenanceLog

class MaintenanceSerializer(serializers.ModelSerializer):
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    completed_by_name = serializers.CharField(source='completed_by.username', read_only=True)
    assigned_to_names = serializers.SerializerMethodField()

    class Meta:
        model = Maintenance
        fields = '__all__'

    def get_assigned_to_names(self, obj):
        return [user.username for user in obj.assigned_to.all()]

class MaintenanceImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaintenanceImage
        fields = ['id', 'image']

class MaintenanceUpdateSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='user.username')
    user_avatar = serializers.CharField(source='user.avatar.url', allow_null=True)
    images = MaintenanceImageSerializer(many=True, read_only=True)

    class Meta:
        model = MaintenanceUpdate
        fields = ['id', 'maintenance', 'user', 'user_name', 'user_avatar', 'message', 'timestamp', 'images']
        read_only_fields = ['user', 'timestamp']

class MaintenanceLogSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = MaintenanceLog
        fields = ['id', 'change_type', 'old_value', 'new_value', 'timestamp', 'description', 'user_name']







