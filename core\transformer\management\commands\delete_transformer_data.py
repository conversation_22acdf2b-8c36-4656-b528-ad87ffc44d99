from django.core.management.base import BaseCommand
from transformer.models import Basestation, TransformerData, Inspection, LvFeeder

class Command(BaseCommand):
    help = 'Delete all records and clean up migrations'

    def handle(self, *args, **kwargs):
        # Delete all records from each model
        self.stdout.write("Deleting all LvFeeder records...")
        LvFeeder.objects.all().delete()

        self.stdout.write("Deleting all Inspection records...")
        Inspection.objects.all().delete()

        self.stdout.write("Deleting all TransformerData records...")
        TransformerData.objects.all().delete()

        self.stdout.write("Deleting all Basestation records...")
        Basestation.objects.all().delete()

        self.stdout.write(self.style.SUCCESS("Successfully deleted all records from transformer models"))


