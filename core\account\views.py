from django.shortcuts import render, get_object_or_404
from rest_framework import generics, viewsets, status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.decorators import api_view, permission_classes, action
from .models import Permission, Role, User, Region, CSCCenter, Substation, Feeder
from .serializers import (
    PermissionSerializer, 
    RoleSerializer, 
    UserSerializer, 
    LoginSerializer, 
    RegisterSerializer, 
    RegionSerializer,
    RegionOnlySerializer,
    CSCSerializer,
    PermissionSerializerNested,
    SubstationSerializer,
    FeederSerializer
)
from django.contrib.auth.hashers import check_password
from rest_framework.parsers import <PERSON>PartPars<PERSON>, Form<PERSON>arser, JSONParser
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.conf import settings
from logs.utils import log_activity, log_error
from django.contrib.auth.tokens import default_token_generator
from django.core.mail import send_mail
from rest_framework.decorators import api_view
from rest_framework import status
from django.core.mail import EmailMessage
from django.template.loader import render_to_string
import logging
from django.template import loader
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.contrib.auth.tokens import default_token_generator
from django.core.mail import EmailMultiAlternatives
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

logger = logging.getLogger(__name__)

User = get_user_model()

# class PermissionListView(APIView):
#     def post(self, request):
#         serializer = PermissionSerializer(data=request.data)
#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# class RoleListView(APIView):
#     def post(self, request):
#         serializer = RoleSerializer(data=request.data)
#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# class UserListView(APIView):
    
#     def post(self, request):
#         if request.user.is_authenticated:
#             print("urequest.user",request.user)
#             request._current_user = request.user
#         else:
#             print("urequest.user","no user")
#             request._current_user = None
#             request._current_user = request.user
#         serializer = UserSerializer(data=request.data)
#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
class LoginView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            return Response(serializer.validated_data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    

class RegisterView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = RegisterSerializer(data=request.data)
        if serializer.is_valid():
            # Save the user and generate tokens
            response_data = serializer.create(serializer.validated_data)
            print("response_data", response_data)
            return Response(response_data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    



# # View to get all regions and create a new region
# class RegionListView(generics.ListCreateAPIView):
#     queryset = Region.objects.all()  # Query all regions
#     serializer_class = RegionSerializer  # Use the RegionSerializer

#     def list(self, request, *args, **kwargs):
#         """Retrieve all top-level regions with their children."""
#         # Filter for only top-level regions (parent is null)
#         queryset = self.get_queryset().filter(parent__isnull=True)
#         serializer = self.get_serializer(queryset, many=True)  # Serialize the data
#         return Response(serializer.data, status=status.HTTP_200_OK)

#     def create(self, request, *args, **kwargs):
#         """Create a new region."""
#         serializer = self.get_serializer(data=request.data)  # Initialize the serializer with incoming data
#         if serializer.is_valid():  # Validate the data
#             serializer.save()  # Save the new region
#             return Response(serializer.data, status=status.HTTP_201_CREATED)  # Return the created region
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)  # Return errors if invalid


# # View to retrieve, update, or delete a specific region by 'key'
# class RegionDetailView(generics.RetrieveUpdateDestroyAPIView):
#     queryset = Region.objects.all()  # Query all regions
#     serializer_class = RegionSerializer  # Use the RegionSerializer
#     lookup_field = 'key'  # Use 'key' as the lookup field instead of 'id'

#     def retrieve(self, request, *args, **kwargs):
#         """Retrieve a specific region by its 'key'."""
#         instance = self.get_object()  # Get the region object
#         serializer = self.get_serializer(instance)  # Serialize the data
#         return Response(serializer.data, status=status.HTTP_200_OK)  # Return serialized data

#     def update(self, request, *args, **kwargs):
#         """Update a specific region by its 'key'."""
#         partial = kwargs.pop('partial', False)  # Allow partial updates
#         instance = self.get_object()  # Get the region object
#         serializer = self.get_serializer(instance, data=request.data, partial=partial)  # Serialize the data
#         if serializer.is_valid():  # Validate the data
#             serializer.save()  # Save the updated region
#             return Response(serializer.data, status=status.HTTP_200_OK)  # Return the updated region
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)  # Return errors if invalid

#     def destroy(self, request, *args, **kwargs):
#         """Delete a specific region by its 'key'."""
#         instance = self.get_object()  # Get the region object
#         instance.delete()  # Delete the region
#         return Response({"detail": "Region deleted successfully."}, status=status.HTTP_204_NO_CONTENT)  # Confirm deletion



# CRUD for Regions
class RegionListCreateView(generics.ListCreateAPIView):
    queryset = Region.objects.all()
    serializer_class = RegionSerializer

class RegionListOnlyCreateView(generics.ListCreateAPIView):
    queryset = Region.objects.all()
    serializer_class = RegionOnlySerializer



class RegionRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Region.objects.all()
    serializer_class = RegionSerializer
    lookup_field = 'csc_code'
    
    def get_object(self):
        """
        Override get_object to handle the lookup properly
        """
        queryset = self.get_queryset()
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}
        
        obj = get_object_or_404(queryset, **filter_kwargs)
        self.check_object_permissions(self.request, obj)
        return obj

# CRUD for CSCs
class CSCListCreateView(generics.ListCreateAPIView):
    queryset = CSCCenter.objects.all()  # Change CSC to CSCCenter
    serializer_class = CSCSerializer

class CSCRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = CSCCenter.objects.all()  # Change CSC to CSCCenter
    serializer_class = CSCSerializer
    lookup_field = 'csc_code'

# Get all CSCs filtered by Region
class CSCByRegionView(generics.ListAPIView):
    serializer_class = CSCSerializer

    def get_queryset(self):
        region_code = self.kwargs['region_code']
        return CSCCenter.objects.filter(region__csc_code=region_code)  # Change CSC to CSCCenter
    



class PermissionViewSet(viewsets.ModelViewSet):
    queryset = Permission.objects.all()
    serializer_class = PermissionSerializer

    def list(self, request, *args, **kwargs):
        """
        Get a list of all permissions.
        """
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    # def create(self, request, *args, **kwargs):
    #     """
    #     Create a new permission with auto-generated ID.
    #     """
    #     serializer = self.get_serializer(data=request.data)
    #     serializer.is_valid(raise_exception=True)
    #     serializer.save()  # Automatically generates the ID
    #     return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    def create(self, request, *args, **kwargs):
        """
        Create a new permission with auto-generated ID and handle errors.
        """
        try:
            # Deserialize and validate the incoming data
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # Save the instance (auto-generates the ID)
            serializer.save()

            # Return the created object with a 201 status code
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            # Handle any unexpected exceptions
            error_message = f"Error creating permission: {str(e)}"
            print(error_message)  # Print the error for debugging
            return Response(
                {"error": error_message},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def retrieve(self, request, *args, **kwargs):
        """
        Get a single permission by ID.
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        """
        Update an existing permission.
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        """
        Delete a permission by ID.
        """
        instance = self.get_object()
        instance.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
    

class PopulatedPermissionViewSet(generics.ListAPIView):
    serializer_class = PermissionSerializerNested

    def get_queryset(self):
        # Get the base queryset
        queryset = Permission.objects.filter(parentId__isnull=True).prefetch_related('children')
        return queryset
    
    

class RoleViewSet(viewsets.ModelViewSet):
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    # permission_classes = [IsAuthenticated]

    # Optional: Add custom actions if needed
    @action(detail=False, methods=['get'])
    def all_roles(self, request):
        roles = Role.objects.all()
        serializer = self.get_serializer(roles, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def role_permissions(self, request, pk=None):
        role = self.get_object()
        permissions = role.permissions.all()
        serializer = PermissionSerializer(permissions, many=True)
        return Response(serializer.data)
    
class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    parser_classes = (JSONParser, MultiPartParser, FormParser)
     # permission_classes = [IsAuthenticated]

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        print("Received data:", request.data)
        print("Content-Type:", request.content_type)
        
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response(
                UserSerializer(user).data,
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=False, methods=['get'])
    def current_user(self, request):
        """Return the currently authenticated user."""
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)

    @action(
        detail=True,
        methods=['patch'],
        url_path='avatar',
        parser_classes=(MultiPartParser,)
    )
    def update_avatar(self, request, pk=None):
        try:
            user = self.get_object()
            avatar_file = request.FILES.get('avatar')
            
            if not avatar_file:
                return Response(
                    {'error': 'No avatar file provided'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate file type
            allowed_types = ['image/jpeg', 'image/png', 'image/gif']
            if avatar_file.content_type not in allowed_types:
                return Response(
                    {'error': 'Invalid file type. Only JPEG, PNG and GIF are allowed.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Delete old avatar if exists
            if user.avatar:
                old_avatar_path = os.path.join(settings.MEDIA_ROOT, str(user.avatar))
                if os.path.isfile(old_avatar_path):
                    default_storage.delete(old_avatar_path)

            # Save new avatar
            user.avatar = avatar_file
            user.save()

            return Response({
                'message': 'Avatar updated successfully',
                'avatar_url': request.build_absolute_uri(user.avatar.url)
            })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'], permission_classes=[IsAuthenticated])
    def password(self, request):
        """
        Change user password.
        Required fields in request.data:
        - old_password
        - new_password
        """
        user = request.user
        old_password = request.data.get('old_password')
        new_password = request.data.get('new_password')

        # Validate input
        if not old_password or not new_password:
            return Response(
                {'error': 'Both old_password and new_password are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if old password is correct
        if not check_password(old_password, user.password):
            return Response(
                {'error': 'Current password is incorrect'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Set new password
        try:
            user.set_password(new_password)
            user.save()
            return Response(
                {'message': 'Password successfully updated'},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class SubstationListCreateView(generics.ListCreateAPIView):
    queryset = Substation.objects.all()
    serializer_class = SubstationSerializer
    permission_classes = [IsAuthenticated]

    def create(self, request, *args, **kwargs):
        try:
            region_code = request.data.get('region')
            name = request.data.get('name')

            # Validate required fields
            if not region_code or not name:
                return Response(
                    {'error': 'Region and name are required fields'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get the region
            try:
                region = Region.objects.get(csc_code=region_code)
            except Region.DoesNotExist:
                return Response(
                    {'error': 'Region not found'}, 
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check if substation with same name exists in the same region
            if Substation.objects.filter(name=name, region=region).exists():
                return Response(
                    {'error': 'Substation with this name already exists in this region'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create the substation
            substation = Substation.objects.create(
                name=name,
                region=region
            )

            serializer = self.get_serializer(substation)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_400_BAD_REQUEST
            )

class SubstationRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Substation.objects.all()
    serializer_class = SubstationSerializer
    lookup_field = 'id'
    permission_classes = [IsAuthenticated]

    def destroy(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

# Get all Substations filtered by Region
class SubstationByRegionView(generics.ListAPIView):
    serializer_class = SubstationSerializer

    def get_queryset(self):
        region_code = self.kwargs['region_code']
        return Substation.objects.filter(region__csc_code=region_code)

class FeederListCreateView(generics.ListCreateAPIView):
    queryset = Feeder.objects.all()
    serializer_class = FeederSerializer
    # permission_classes = [IsAuthenticated]

    def create(self, request, *args, **kwargs):
        try:
            substation_id = request.data.get('substation')
            substation = Substation.objects.get(id=substation_id)
            
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            serializer.save(substation=substation)
            
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Substation.DoesNotExist:
            return Response(
                {'error': 'Substation not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_400_BAD_REQUEST
            )

class FeederRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Feeder.objects.all()
    serializer_class = FeederSerializer
    # permission_classes = [IsAuthenticated]
    lookup_field = 'id'

# Get all Feeders filtered by Substation
class FeederBySubstationView(generics.ListAPIView):
    serializer_class = FeederSerializer
    # permission_classes = [IsAuthenticated]

    def get_queryset(self):
        substation_id = self.kwargs['substation_id']
        return Feeder.objects.filter(substation_id=substation_id)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_substation(request):
    try:
        region_code = request.data.get('region')
        name = request.data.get('name')

        # Validate required fields
        if not region_code or not name:
            return Response(
                {'error': 'Region and name are required fields'}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the region
        try:
            region = Region.objects.get(csc_code=region_code)
        except Region.DoesNotExist:
            return Response(
                {'error': 'Region not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if substation with same name exists
        if Substation.objects.filter(name=name).exists():
            return Response(
                {'error': 'Substation with this name already exists'}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create the substation
        substation = Substation.objects.create(
            name=name,
            region=region
        )

        return Response({
            'id': substation.id,
            'name': substation.name,
            'region': region.name
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        print("eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee", e)
        return Response(
            {'error': str(e)}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_substation(request, id):
    try:
        substation = Substation.objects.get(id=id)
        region_code = request.data.get('region')
        new_name = request.data.get('name')

        # Validate required fields
        if not region_code or not new_name:
            return Response(
                {'error': 'Region and name are required fields'}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the region
        try:
            region = Region.objects.get(csc_code=region_code)
        except Region.DoesNotExist:
            return Response(
                {'error': 'Region not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if new name is already taken (excluding current substation)
        if (new_name != substation.name and 
            Substation.objects.filter(name=new_name).exists()):
            return Response(
                {'error': 'Substation with this name already exists'}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update the substation
        substation.name = new_name
        substation.region = region
        substation.save()

        return Response({
            'id': substation.id,
            'name': substation.name,
            'region': region.name
        })

    except Substation.DoesNotExist:
        return Response(
            {'error': 'Substation not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': str(e)}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def Memail(mto, mfrom, msubject, mbody, user_active):
    """
    Sends an email using EmailMultiAlternatives.
    """
    try:
        msg = EmailMultiAlternatives(msubject, mbody, mfrom, [mto])
        msg.attach_alternative(mbody, "text/html")
        msg.send()
    except Exception as e:
        logger.error(f"Failed to send email: {str(e)}", exc_info=True)
        raise

@api_view(['POST'])
def reset_password(request):
    if request.method == "POST":
        try:
            # Extract email from the request
            email = request.data.get('email')
            if not email:
                return Response({"message": "Email is required"}, status=status.HTTP_400_BAD_REQUEST)

            # Check if the user exists
            user = User.objects.filter(email=email).first()

            if user:
                # Generate a password reset token
                token = default_token_generator.make_token(user)
                uid = urlsafe_base64_encode(force_bytes(user.pk))
                
                # Create the reset link
                reset_url = f"{settings.FRONTEND_URL}/reset-password?uid={uid}&token={token}"
                
                # Render the email template
                template = loader.get_template('email/password_reset_email.html')
                context = {
                    'user': user,
                    'reset_url': reset_url,
                    'support_email': settings.DEFAULT_FROM_EMAIL,
                }
                rendered_email = template.render(context)
                
                # Send the email using Memail
                subject = "Password Reset Request - Your Application"
                mto = email
                mfrom = settings.DEFAULT_FROM_EMAIL
                Memail(mto, mfrom, subject, rendered_email, user_active=True)
                
                return Response(
                    {"message": "Password reset email sent successfully"},
                    status=status.HTTP_200_OK
                )
            
            else:
                # Return a generic response for security reasons
                return Response(
                    {"message": "If the email exists, a password reset link has been sent"},
                    status=status.HTTP_200_OK
                )
        
        except Exception as e:
            # Log the error and return a generic failure message
            logger.error(f"Error during password reset: {str(e)}", exc_info=True)
            return Response(
                {"message": "Failed to send reset email", 'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    return Response({"message": "Invalid request method."}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
def confirm_reset_password(request):
    try:
        # Get the uid, token and new password from request
        uid = request.data.get('uid')
        token = request.data.get('token')
        new_password = request.data.get('new_password')
        
        if not all([uid, token, new_password]):
            return Response(
                {"message": "Missing required fields"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Decode the user ID
        try:
            uid = urlsafe_base64_decode(uid).decode()
            user = User.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            return Response(
                {"message": "Invalid reset link"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if the token is valid
        if default_token_generator.check_token(user, token):
            # Set the new password
            user.set_password(new_password)
            user.save()
            return Response(
                {"message": "Password has been reset successfully"},
                status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"message": "Invalid or expired reset link"},
                status=status.HTTP_400_BAD_REQUEST
            )

    except Exception as e:
        logger.error(f"Error during password reset confirmation: {str(e)}", exc_info=True)
        return Response(
            {"message": "Failed to reset password"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
# @permission_classes([IsAdminUser])
def admin_reset_password(request, user_id):
    try:
        # Get the user instance
        user = User.objects.get(id=user_id)
        
        # Get the new password from request data
        new_password = request.data.get('new_password')
        if not new_password:
            return Response(
                {"message": "New password is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Set the new password
        user.set_password(new_password)
        user.save()

        return Response(
            {"message": "Password has been reset successfully"},
            status=status.HTTP_200_OK
        )

    except User.DoesNotExist:
        return Response(
            {"message": "User not found"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"message": "Failed to reset password"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['PATCH'])
# @permission_classes([IsAdminUser])
def admin_update_user_profile(request, user_id):
    try:
        # Get the user instance
        user = User.objects.get(id=user_id)
        
        # Update user fields
        if 'username' in request.data:
            user.username = request.data['username']
        if 'email' in request.data:
            user.email = request.data['email']
        if 'phone' in request.data:
            user.phone = request.data['phone']
        if 'full_name' in request.data:
            user.full_name = request.data['full_name']
        if 'region' in request.data:
            user.region = request.data['region']
        if 'csc' in request.data:
            user.csc = request.data['csc']
        if 'title' in request.data:
            user.title = request.data['title']
        if 'address' in request.data:
            user.address = request.data['address']
        if 'city' in request.data:
            user.city = request.data['city']
        if 'about' in request.data:
            user.about = request.data['about']

        # Save the updated user
        user.save()

        return Response(
            {"message": "User profile updated successfully"},
            status=status.HTTP_200_OK
        )

    except User.DoesNotExist:
        return Response(
            {"message": "User not found"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"message": "Failed to update user profile"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

class LogoutView(APIView):
    def post(self, request):
        try:
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            return Response({'message': 'Successfully logged out.'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

