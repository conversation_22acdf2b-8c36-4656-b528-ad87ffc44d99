from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

app_name = "account"

router = DefaultRouter()
# Synchronous ViewSets (original)
router.register(r'permissions', views.PermissionViewSet, basename='permission')
router.register(r'roles', views.RoleViewSet, basename='role')
router.register(r'users', views.UserViewSet, basename='user')

# Async ViewSets for high concurrency (production-ready)
router.register(r'async/users', views.AsyncUserViewSet, basename='async_users')
router.register(r'async/roles', views.AsyncRoleViewSet, basename='async_roles')
router.register(r'async/regions', views.AsyncRegionViewSet, basename='async_regions')

urlpatterns = [
    path('signin', views.LoginView.as_view(), name='login'),
    path('signup', views.RegisterView.as_view(), name='user-list'),
    path('logout', views.LogoutView.as_view(), name='logout'),
    
    # Region CRUD
    path('regions/', views.RegionListCreateView.as_view(), name='region-list-create'),
    path('regionsOnly/', views.RegionListOnlyCreateView.as_view(), name='region-list-create'),
    path('regions/<str:csc_code>/', views.RegionRetrieveUpdateDestroyView.as_view(), name='region-retrieve-update-destroy'),

    # CSC CRUD
    path('csc/', views.CSCListCreateView.as_view(), name='csc-list-create'),
    path('csc/<str:csc_code>/', views.CSCRetrieveUpdateDestroyView.as_view(), name='csc-retrieve-update-destroy'),
    path('regions/<str:region_code>/csc/', views.CSCByRegionView.as_view(), name='csc-by-region'),

    path('', include(router.urls)),
    path('populatedPermissions/', views.PopulatedPermissionViewSet.as_view(), name='populatedPermission'),
    
    # Substation endpoints
    path('substation/', views.SubstationListCreateView.as_view(), name='substation-list-create'),
    path('substation/<int:id>/', views.SubstationRetrieveUpdateDestroyView.as_view(), name='substation-detail'),

    # Feeder endpoints - updated paths
    path('feeders/', views.FeederListCreateView.as_view(), name='feeder-list-create'),
    path('feeders/<int:id>/', views.FeederRetrieveUpdateDestroyView.as_view(), name='feeder-detail'),
    path('substations/<str:substation_id>/feeders/', views.FeederBySubstationView.as_view(), name='feeder-by-substation'),
    path('reset-password/', views.reset_password, name='reset_password'),
    path('confirm-reset-password/', views.confirm_reset_password, name='confirm_reset_password'),
    path('users/<str:user_id>/admin-reset-password/', views.admin_reset_password, name='admin-reset-password'),
    path('users/<str:user_id>/profile/', views.admin_update_user_profile, name='admin-update-user-profile'),
]
