from decimal import Decimal
import json
from datetime import date, datetime

# Async imports
from channels.db import database_sync_to_async
import asyncio
from asgiref.sync import sync_to_async

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        elif isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

def serialize_data(data):
    """Helper function to serialize data including Decimal and date types"""
    if isinstance(data, dict):
        return {k: serialize_data(v) for k, v in data.items()}
    elif isinstance(data, (list, tuple)):
        return [serialize_data(item) for item in data]
    elif isinstance(obj, Decimal):
        return str(obj)
    elif isinstance(obj, (date, datetime)):
        return obj.isoformat()
    return data

def log_activity(user, action, model_name, record_id, changes, ip_address=None):
    from .models import ActivityLog

    # Serialize the changes data
    serialized_changes = json.dumps(changes, cls=CustomJSONEncoder)

    ActivityLog.objects.create(
        user=user,
        action=action,
        model_name=model_name,
        record_id=str(record_id),
        changes=serialized_changes,
        ip_address=ip_address
    )

def log_error(level, message, user=None, traceback=None, ip_address=None):
    from .models import ErrorLog

    # Serialize any data that might contain Decimal or date
    error_data = {
        'message': message,
        'traceback': traceback
    }
    serialized_data = json.dumps(error_data, cls=CustomJSONEncoder)

    ErrorLog.objects.create(
        level=level,
        message=message,  # Original message
        user=user,
        traceback=traceback,  # Original traceback
        ip_address=ip_address
    )

def log_data_change(model_instance, field_name, old_value, new_value,reason, user, ip_address=None):
    from .models import DataChangeLog

    # Define model mapping with their prefixes and identifier fields
    MODEL_MAPPING = {
        'basestation': {
            'display_name': 'Basestation',
            'id_field': 'station_code',
            'prefix': 'BS-'
        },
        'transformerdata': {
            'display_name': 'Transformer Data',
            'id_field': 'id',  # Using id instead of dt_number
            'prefix': 'TR-'
        },
        'inspection': {
            'display_name': 'Inspection',
            'id_field': 'id',
            'prefix': 'IN-'
        },
        'lvfeeder': {
            'display_name': 'LV Feeder',
            'id_field': 'id',
            'prefix': 'LV-'
        }
    }

    print('model_instance:', model_instance)

    model_name = model_instance._meta.model_name.lower()
    model_info = MODEL_MAPPING.get(model_name)

    print('model_name:', model_name)
    print('model_info:', model_info)

    if model_info:
        display_name = model_info['display_name']
        id_field = model_info['id_field']
        prefix = model_info['prefix']

        # Get the identifier value safely with better error handling
        try:
            identifier = getattr(model_instance, id_field)
            if identifier is None:
                identifier = model_instance.pk
        except AttributeError:
            identifier = model_instance.pk

        record_id = f"{prefix}{identifier}"
    else:
        display_name = model_name.title()
        record_id = str(model_instance.pk)

    print('display_name:', display_name)
    print('record_id:', record_id)

    # Handle model instances in old_value and new_value
    if hasattr(old_value, '_meta'):
        old_value = str(getattr(old_value, 'station_code', old_value.pk))
    if hasattr(new_value, '_meta'):
        new_value = str(getattr(new_value, 'station_code', new_value.pk))

    # Create the log entry
    log_entry = DataChangeLog.objects.create(
        model_name=display_name,
        record_id=record_id,
        field_name=field_name,
        old_value=str(old_value) if old_value is not None else None,
        new_value=str(new_value) if new_value is not None else None,
        reason=reason,
        changed_by=user,
        ip_address=ip_address
    )

    # Set the appropriate foreign key
    if model_name == 'basestation':
        log_entry.basestation = model_instance
    elif model_name == 'transformerdata':
        log_entry.transformer_data = model_instance
    elif model_name == 'inspection':
        log_entry.inspection = model_instance
    elif model_name == 'lvfeeder':
        log_entry.lv_feeder = model_instance

    log_entry.save()








# ============================================================================
# ASYNC LOGGING UTILITIES FOR HIGH CONCURRENCY PRODUCTION USE
# ============================================================================

async def async_log_activity(user, action, model_name, record_id, changes, ip_address=None):
    """
    Async version of log_activity for high concurrency logging without blocking.
    """
    from .models import ActivityLog

    try:
        # Serialize the changes data
        serialized_changes = json.dumps(changes, cls=CustomJSONEncoder)

        # Create log entry asynchronously
        await database_sync_to_async(ActivityLog.objects.create)(
            user=user,
            action=action,
            model_name=model_name,
            record_id=str(record_id),
            changes=serialized_changes,
            ip_address=ip_address
        )
    except Exception as e:
        # If logging fails, don't crash the main operation
        # Could optionally log to a fallback system here
        pass

async def async_log_error(level, message, user=None, traceback=None, ip_address=None):
    """
    Async version of log_error for high concurrency error logging without blocking.
    """
    from .models import ErrorLog

    try:
        # Create error log entry asynchronously
        await database_sync_to_async(ErrorLog.objects.create)(
            level=level,
            message=message,
            user=user,
            traceback=traceback,
            ip_address=ip_address
        )
    except Exception as e:
        # If error logging fails, don't crash the main operation
        # Could optionally log to a fallback system here
        pass

async def async_log_data_change(model_instance, field_name, old_value, new_value, reason, user, ip_address=None):
    """
    Async version of log_data_change for high concurrency data change logging without blocking.
    """
    from .models import DataChangeLog

    try:
        # Define model mapping with their prefixes and identifier fields
        MODEL_MAPPING = {
            'basestation': {
                'display_name': 'Basestation',
                'id_field': 'station_code',
                'prefix': 'BS-'
            },
            'transformerdata': {
                'display_name': 'Transformer Data',
                'id_field': 'id',
                'prefix': 'TR-'
            },
            'inspection': {
                'display_name': 'Inspection',
                'id_field': 'id',
                'prefix': 'IN-'
            },
            'lvfeeder': {
                'display_name': 'LV Feeder',
                'id_field': 'id',
                'prefix': 'LV-'
            }
        }

        model_name = model_instance._meta.model_name.lower()
        model_info = MODEL_MAPPING.get(model_name)

        if model_info:
            display_name = model_info['display_name']
            id_field = model_info['id_field']
            prefix = model_info['prefix']

            # Get the identifier value safely
            try:
                identifier = getattr(model_instance, id_field)
                if identifier is None:
                    identifier = model_instance.pk
            except AttributeError:
                identifier = model_instance.pk

            record_id = f"{prefix}{identifier}"
        else:
            display_name = model_name.title()
            record_id = str(model_instance.pk)

        # Handle model instances in old_value and new_value
        if hasattr(old_value, '_meta'):
            old_value = str(getattr(old_value, 'station_code', old_value.pk))
        if hasattr(new_value, '_meta'):
            new_value = str(getattr(new_value, 'station_code', new_value.pk))

        # Create the log entry asynchronously
        log_entry = await database_sync_to_async(DataChangeLog.objects.create)(
            model_name=display_name,
            record_id=record_id,
            field_name=field_name,
            old_value=str(old_value) if old_value is not None else None,
            new_value=str(new_value) if new_value is not None else None,
            reason=reason,
            changed_by=user,
            ip_address=ip_address
        )

        # Set the appropriate foreign key asynchronously
        if model_name == 'basestation':
            log_entry.basestation = model_instance
        elif model_name == 'transformerdata':
            log_entry.transformer_data = model_instance
        elif model_name == 'inspection':
            log_entry.inspection = model_instance
        elif model_name == 'lvfeeder':
            log_entry.lv_feeder = model_instance

        await database_sync_to_async(log_entry.save)()

    except Exception as e:
        # If data change logging fails, don't crash the main operation
        # Could optionally log to a fallback system here
        pass

# Convenience functions that automatically choose sync or async based on context
def smart_log_activity(user, action, model_name, record_id, changes, ip_address=None):
    """
    Smart logging function that automatically uses async if in async context.
    """
    try:
        # Try to get the current event loop
        loop = asyncio.get_running_loop()
        # If we're in an async context, use async logging
        return async_log_activity(user, action, model_name, record_id, changes, ip_address)
    except RuntimeError:
        # No event loop running, use sync logging
        return log_activity(user, action, model_name, record_id, changes, ip_address)

def smart_log_error(level, message, user=None, traceback=None, ip_address=None):
    """
    Smart error logging function that automatically uses async if in async context.
    """
    try:
        # Try to get the current event loop
        loop = asyncio.get_running_loop()
        # If we're in an async context, use async logging
        return async_log_error(level, message, user, traceback, ip_address)
    except RuntimeError:
        # No event loop running, use sync logging
        return log_error(level, message, user, traceback, ip_address)
