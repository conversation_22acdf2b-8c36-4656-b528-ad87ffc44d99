#!/usr/bin/env python
"""
Comprehensive test script for all async ViewSets
Tests concurrent requests to verify all async implementations handle high load without connection issues.
"""

import asyncio
import aiohttp
import time
import json
from concurrent.futures import ThreadPoolExecutor
import threading

# Configuration
BASE_URL = "http://localhost:8000/api/transformer"
NUM_CONCURRENT_REQUESTS = 15
ASYNC_ENDPOINTS = [
    "/async/basestations/",
    "/async/transformerdata/",
    "/async/inspections/",
    "/async/lvfeeders/",
    "/basestationsFilteredAsync/"
]

async def make_request(session, url, request_id, endpoint_name):
    """Make a single async request"""
    try:
        start_time = time.time()
        async with session.get(url, params={"pageSize": "20"}) as response:
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status == 200:
                data = await response.json()
                return {
                    'request_id': request_id,
                    'endpoint': endpoint_name,
                    'status': 'success',
                    'response_time': response_time,
                    'status_code': response.status,
                    'data_count': len(data.get('results', [])) if isinstance(data, dict) else len(data) if isinstance(data, list) else 0,
                    'async_processed': data.get('async_processed', False) if isinstance(data, dict) else False
                }
            else:
                error_text = await response.text()
                return {
                    'request_id': request_id,
                    'endpoint': endpoint_name,
                    'status': 'error',
                    'response_time': response_time,
                    'status_code': response.status,
                    'error': error_text[:200]
                }
    except Exception as e:
        return {
            'request_id': request_id,
            'endpoint': endpoint_name,
            'status': 'exception',
            'response_time': 0,
            'error': str(e)
        }

async def test_endpoint_concurrency(endpoint, num_requests=5):
    """Test a single endpoint with concurrent requests"""
    print(f"\n🔄 Testing {endpoint} with {num_requests} concurrent requests...")
    
    connector = aiohttp.TCPConnector(
        limit=30,
        limit_per_host=20,
        ttl_dns_cache=300,
        use_dns_cache=True,
    )
    
    timeout = aiohttp.ClientTimeout(total=60)
    
    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
        tasks = []
        for i in range(num_requests):
            url = f"{BASE_URL}{endpoint}"
            task = make_request(session, url, i + 1, endpoint)
            tasks.append(task)
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        total_time = end_time - start_time
        successful = [r for r in results if isinstance(r, dict) and r.get('status') == 'success']
        failed = [r for r in results if isinstance(r, dict) and r.get('status') != 'success']
        exceptions = [r for r in results if not isinstance(r, dict)]
        
        success_rate = (len(successful) / num_requests) * 100
        
        print(f"  ✅ Success: {len(successful)}/{num_requests} ({success_rate:.1f}%)")
        print(f"  ❌ Failed: {len(failed)}")
        print(f"  💥 Exceptions: {len(exceptions)}")
        print(f"  ⏱️  Total time: {total_time:.2f}s")
        
        if successful:
            avg_time = sum(r['response_time'] for r in successful) / len(successful)
            print(f"  📊 Avg response time: {avg_time:.2f}s")
            
            async_count = sum(1 for r in successful if r.get('async_processed'))
            if async_count > 0:
                print(f"  🚀 Async processed: {async_count}/{len(successful)}")
        
        if failed:
            print(f"  ⚠️  Sample failures:")
            for failure in failed[:2]:
                print(f"    - {failure.get('error', 'Unknown error')[:80]}")
        
        return {
            'endpoint': endpoint,
            'total': num_requests,
            'successful': len(successful),
            'failed': len(failed),
            'exceptions': len(exceptions),
            'success_rate': success_rate,
            'total_time': total_time
        }

async def run_comprehensive_test():
    """Run comprehensive test on all async endpoints"""
    print("🚀 Starting comprehensive async ViewSet testing...")
    print(f"📍 Base URL: {BASE_URL}")
    print(f"🔢 Testing {len(ASYNC_ENDPOINTS)} endpoints")
    print("="*60)
    
    all_results = []
    
    # Test each endpoint
    for endpoint in ASYNC_ENDPOINTS:
        result = await test_endpoint_concurrency(endpoint, 5)
        all_results.append(result)
        await asyncio.sleep(1)  # Brief pause between endpoint tests
    
    # Summary
    print("\n" + "="*60)
    print("📊 COMPREHENSIVE TEST SUMMARY")
    print("="*60)
    
    total_requests = sum(r['total'] for r in all_results)
    total_successful = sum(r['successful'] for r in all_results)
    total_failed = sum(r['failed'] for r in all_results)
    total_exceptions = sum(r['exceptions'] for r in all_results)
    overall_success_rate = (total_successful / total_requests) * 100 if total_requests > 0 else 0
    
    print(f"📈 Overall Results:")
    print(f"  Total requests: {total_requests}")
    print(f"  Successful: {total_successful}")
    print(f"  Failed: {total_failed}")
    print(f"  Exceptions: {total_exceptions}")
    print(f"  Success rate: {overall_success_rate:.1f}%")
    
    print(f"\n📋 Per-Endpoint Results:")
    for result in all_results:
        status_icon = "✅" if result['success_rate'] >= 80 else "⚠️" if result['success_rate'] >= 60 else "❌"
        print(f"  {status_icon} {result['endpoint']}: {result['success_rate']:.1f}% ({result['successful']}/{result['total']})")
    
    # Overall assessment
    if overall_success_rate >= 95:
        print(f"\n🎉 EXCELLENT: {overall_success_rate:.1f}% success rate - All async ViewSets are production-ready!")
    elif overall_success_rate >= 80:
        print(f"\n✅ GOOD: {overall_success_rate:.1f}% success rate - Async ViewSets are working well with minor issues")
    elif overall_success_rate >= 60:
        print(f"\n⚠️  WARNING: {overall_success_rate:.1f}% success rate - Some async ViewSets need attention")
    else:
        print(f"\n❌ CRITICAL: {overall_success_rate:.1f}% success rate - Async ViewSets need significant fixes")
    
    return all_results

async def run_high_load_test():
    """Run a high-load test with many concurrent requests"""
    print(f"\n🔥 HIGH LOAD TEST: {NUM_CONCURRENT_REQUESTS} concurrent requests across all endpoints")
    print("="*60)
    
    connector = aiohttp.TCPConnector(
        limit=50,
        limit_per_host=30,
        ttl_dns_cache=300,
        use_dns_cache=True,
    )
    
    timeout = aiohttp.ClientTimeout(total=120)
    
    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
        tasks = []
        for i in range(NUM_CONCURRENT_REQUESTS):
            endpoint = ASYNC_ENDPOINTS[i % len(ASYNC_ENDPOINTS)]
            url = f"{BASE_URL}{endpoint}"
            task = make_request(session, url, i + 1, endpoint)
            tasks.append(task)
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        total_time = end_time - start_time
        successful = [r for r in results if isinstance(r, dict) and r.get('status') == 'success']
        failed = [r for r in results if isinstance(r, dict) and r.get('status') != 'success']
        exceptions = [r for r in results if not isinstance(r, dict)]
        
        success_rate = (len(successful) / NUM_CONCURRENT_REQUESTS) * 100
        
        print(f"🎯 High Load Results:")
        print(f"  Total requests: {NUM_CONCURRENT_REQUESTS}")
        print(f"  Successful: {len(successful)}")
        print(f"  Failed: {len(failed)}")
        print(f"  Exceptions: {len(exceptions)}")
        print(f"  Success rate: {success_rate:.1f}%")
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Requests/second: {NUM_CONCURRENT_REQUESTS/total_time:.1f}")
        
        if successful:
            avg_time = sum(r['response_time'] for r in successful) / len(successful)
            max_time = max(r['response_time'] for r in successful)
            min_time = min(r['response_time'] for r in successful)
            print(f"  Avg response time: {avg_time:.2f}s")
            print(f"  Max response time: {max_time:.2f}s")
            print(f"  Min response time: {min_time:.2f}s")
        
        return success_rate >= 90

def run_sync_test():
    """Run the async test"""
    return asyncio.run(run_comprehensive_test())

if __name__ == "__main__":
    print("🧪 ASYNC VIEWSET COMPREHENSIVE TESTING")
    print("Make sure your Django server is running on localhost:8000")
    print("Press Ctrl+C to cancel\n")
    
    try:
        # Run comprehensive test
        results = asyncio.run(run_comprehensive_test())
        
        # Run high load test
        high_load_success = asyncio.run(run_high_load_test())
        
        print(f"\n🏁 FINAL ASSESSMENT:")
        if high_load_success:
            print("🚀 SUCCESS: All async ViewSets are production-ready for high concurrency!")
        else:
            print("⚠️  NEEDS WORK: Some async ViewSets may need optimization for high load")
            
    except KeyboardInterrupt:
        print("\n❌ Test cancelled by user")
    except Exception as e:
        print(f"\n💥 Test failed with exception: {e}")
