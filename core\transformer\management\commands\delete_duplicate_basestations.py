from django.core.management.base import BaseCommand
from django.db.models import Count
from transformer.models import Basestation  # Replace 'transformer' if your app name is different

class Command(BaseCommand):
    help = 'Delete duplicate basestations not linked to TransformerData'

    def handle(self, *args, **kwargs):
        # Step 1: Find duplicate gps_location values
        duplicate_gps = (
            Basestation.objects
            .values('gps_location')
            .annotate(gps_count=Count('station_code'))
            .filter(gps_count__gt=1)
        )

        # Step 2: Iterate over duplicates
        for entry in duplicate_gps:
            gps = entry['gps_location']
            basestations = Basestation.objects.filter(gps_location=gps)

            # Check if any basestation is linked to TransformerData
            linked = basestations.filter(transformer_data_basestation__isnull=False)

            if linked.exists():
                # Keep the one(s) linked to transformer
                keep_codes = set(linked.values_list('station_code', flat=True))
            else:
                # None linked to transformer, keep only the first one
                keep_codes = {basestations.first().station_code}

            # Delete all other basestations with same gps
            to_delete = basestations.exclude(station_code__in=keep_codes)
            count = to_delete.count()
            to_delete.delete()
            self.stdout.write(f"Deleted {count} unlinked duplicate(s) with GPS location: {gps}")
