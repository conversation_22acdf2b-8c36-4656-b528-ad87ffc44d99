"""
Production configuration for async Django backend.
This file contains all the settings and configurations needed for high-concurrency production deployment.
"""

# ============================================================================
# PRODUCTION ASYNC SETTINGS
# ============================================================================

# Database configuration for production with PostgreSQL
PRODUCTION_DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        'NAME': 'trafo_production',
        'USER': 'trafo_user',
        'PASSWORD': 'your_secure_password_here',
        'HOST': 'localhost',  # or your PostgreSQL server
        'PORT': '5432',
        
        # High concurrency settings
        'CONN_MAX_AGE': 600,  # Keep connections alive for 10 minutes
        'CONN_HEALTH_CHECKS': True,  # Enable connection health checks
        'OPTIONS': {
            # PostgreSQL-specific optimizations
            'options': '-c statement_timeout=30000 -c idle_in_transaction_session_timeout=300000',
            'connect_timeout': 10,
            'application_name': 'transformer_async_backend',
        },
        'ATOMIC_REQUESTS': False,  # Disable for better async performance
        'AUTOCOMMIT': True,  # Enable autocommit for async operations
        
        # Connection pooling (requires django-db-connection-pool)
        'POOL_OPTIONS': {
            'POOL_SIZE': 20,  # Number of connections to maintain
            'MAX_OVERFLOW': 30,  # Additional connections when needed
            'POOL_TIMEOUT': 30,  # Timeout for getting connection
            'POOL_RECYCLE': 3600,  # Recycle connections after 1 hour
        }
    }
}

# Redis configuration for caching and sessions
PRODUCTION_REDIS_CONFIG = {
    'CACHES': {
        'default': {
            'BACKEND': 'django_redis.cache.RedisCache',
            'LOCATION': 'redis://127.0.0.1:6379/1',
            'OPTIONS': {
                'CLIENT_CLASS': 'django_redis.client.DefaultClient',
                'CONNECTION_POOL_KWARGS': {
                    'max_connections': 100,  # Increased for high concurrency
                    'retry_on_timeout': True,
                    'socket_keepalive': True,
                    'socket_keepalive_options': {},
                },
                'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
                'IGNORE_EXCEPTIONS': True,  # Don't crash if Redis is down
                'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
            },
            'TIMEOUT': 300,  # 5 minutes default timeout
            'KEY_PREFIX': 'transformer_prod',
            'VERSION': 1,
        },
        'sessions': {
            'BACKEND': 'django_redis.cache.RedisCache',
            'LOCATION': 'redis://127.0.0.1:6379/2',
            'OPTIONS': {
                'CLIENT_CLASS': 'django_redis.client.DefaultClient',
                'CONNECTION_POOL_KWARGS': {
                    'max_connections': 50,
                    'retry_on_timeout': True,
                },
            },
            'TIMEOUT': 86400,  # 24 hours for sessions
            'KEY_PREFIX': 'transformer_sessions',
        }
    },
    
    # Session configuration
    'SESSION_ENGINE': 'django.contrib.sessions.backends.cache',
    'SESSION_CACHE_ALIAS': 'sessions',
    'SESSION_COOKIE_AGE': 86400,  # 24 hours
    'SESSION_SAVE_EVERY_REQUEST': False,  # Performance optimization
    'SESSION_EXPIRE_AT_BROWSER_CLOSE': False,
}

# Channel layers for async operations
PRODUCTION_CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            'hosts': [('127.0.0.1', 6379)],
            'capacity': 2000,  # Increased for high concurrency
            'expiry': 60,  # Message expiry in seconds
            'group_expiry': 86400,  # Group expiry in seconds
            'symmetric_encryption_keys': ['your-secret-key-here'],
        },
    },
}

# Async-specific settings
PRODUCTION_ASYNC_SETTINGS = {
    'ASYNC_TIMEOUT': 30,
    'ASYNC_BATCH_SIZE': 500,
    'ASYNC_MAX_CONCURRENT_REQUESTS': 200,  # Increased for production
    'ASYNC_REQUEST_TIMEOUT': 300,
    'ASYNC_MIDDLEWARE_ENABLED': True,
    'ASYNC_LOGGING_ENABLED': True,
    'ASYNC_RATE_LIMIT_ENABLED': True,
    'ASYNC_PERFORMANCE_MONITORING': True,
}

# Production logging configuration
PRODUCTION_LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
        'json': {
            'format': '{"level": "{levelname}", "time": "{asctime}", "module": "{module}", "message": "{message}"}',
            'style': '{',
        },
    },
    'handlers': {
        'file_async': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/transformer/async.log',
            'maxBytes': 1024*1024*50,  # 50MB
            'backupCount': 20,
            'formatter': 'json',
        },
        'file_error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/transformer/error.log',
            'maxBytes': 1024*1024*50,  # 50MB
            'backupCount': 20,
            'formatter': 'verbose',
        },
        'console': {
            'level': 'WARNING',  # Only warnings and errors to console in production
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'syslog': {
            'level': 'INFO',
            'class': 'logging.handlers.SysLogHandler',
            'address': '/dev/log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file_async', 'console', 'syslog'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['file_async', 'file_error'],
            'level': 'INFO',
            'propagate': False,
        },
        'transformer': {
            'handlers': ['file_async', 'file_error'],
            'level': 'INFO',
            'propagate': False,
        },
        'account': {
            'handlers': ['file_async', 'file_error'],
            'level': 'INFO',
            'propagate': False,
        },
        'maintenance': {
            'handlers': ['file_async', 'file_error'],
            'level': 'INFO',
            'propagate': False,
        },
        'logs': {
            'handlers': ['file_async', 'file_error'],
            'level': 'INFO',
            'propagate': False,
        },
        'async_middleware': {
            'handlers': ['file_async', 'file_error'],
            'level': 'WARNING',  # Only log warnings and errors from middleware
            'propagate': False,
        },
    },
}

# Security settings for production
PRODUCTION_SECURITY = {
    'DEBUG': False,
    'ALLOWED_HOSTS': ['your-domain.com', 'www.your-domain.com', '127.0.0.1', 'localhost'],
    'SECURE_BROWSER_XSS_FILTER': True,
    'SECURE_CONTENT_TYPE_NOSNIFF': True,
    'SECURE_HSTS_INCLUDE_SUBDOMAINS': True,
    'SECURE_HSTS_PRELOAD': True,
    'SECURE_HSTS_SECONDS': 31536000,  # 1 year
    'SECURE_REDIRECT_EXEMPT': [],
    'SECURE_SSL_REDIRECT': True,
    'SESSION_COOKIE_SECURE': True,
    'CSRF_COOKIE_SECURE': True,
    'X_FRAME_OPTIONS': 'DENY',
    'SECURE_PROXY_SSL_HEADER': ('HTTP_X_FORWARDED_PROTO', 'https'),
}

# CORS settings for production
PRODUCTION_CORS = {
    'CORS_ALLOWED_ORIGINS': [
        'https://your-frontend-domain.com',
        'https://www.your-frontend-domain.com',
    ],
    'CORS_ALLOW_CREDENTIALS': True,
    'CORS_ALLOW_ALL_ORIGINS': False,  # Set to False in production
    'CORS_ALLOWED_HEADERS': [
        'accept',
        'accept-encoding',
        'authorization',
        'content-type',
        'dnt',
        'origin',
        'user-agent',
        'x-csrftoken',
        'x-requested-with',
    ],
}

# ============================================================================
# DEPLOYMENT INSTRUCTIONS
# ============================================================================

DEPLOYMENT_INSTRUCTIONS = """
PRODUCTION DEPLOYMENT GUIDE FOR ASYNC DJANGO BACKEND

1. SERVER REQUIREMENTS:
   - Python 3.9+
   - PostgreSQL 12+
   - Redis 6+
   - Nginx (for reverse proxy)
   - Supervisor or systemd (for process management)

2. INSTALL DEPENDENCIES:
   pip install -r requirements.txt
   pip install gunicorn uvicorn[standard] django-redis psycopg2-binary

3. DATABASE SETUP:
   - Create PostgreSQL database and user
   - Update DATABASES setting with production credentials
   - Run migrations: python manage.py migrate

4. REDIS SETUP:
   - Install and configure Redis server
   - Update CACHES and CHANNEL_LAYERS settings

5. STATIC FILES:
   python manage.py collectstatic --noinput

6. ENVIRONMENT VARIABLES:
   export DJANGO_SETTINGS_MODULE=core.settings_production
   export SECRET_KEY=your-secret-key-here
   export DATABASE_URL=postgresql://user:pass@localhost/dbname
   export REDIS_URL=redis://localhost:6379/1

7. ASGI SERVER CONFIGURATION:
   # Using Uvicorn with Gunicorn
   gunicorn core.asgi:application -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

   # Or using Daphne
   daphne -b 0.0.0.0 -p 8000 core.asgi:application

8. NGINX CONFIGURATION:
   upstream django_async {
       server 127.0.0.1:8000;
   }
   
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://django_async;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_connect_timeout 60s;
           proxy_send_timeout 60s;
           proxy_read_timeout 60s;
       }
   }

9. MONITORING:
   - Set up log rotation for /var/log/transformer/
   - Monitor Redis memory usage
   - Monitor PostgreSQL connection counts
   - Set up health checks for the ASGI application

10. PERFORMANCE TUNING:
    - Adjust worker processes based on CPU cores
    - Tune PostgreSQL shared_buffers and max_connections
    - Configure Redis maxmemory and eviction policies
    - Monitor and adjust async rate limits based on load
"""

# Function to apply production settings
def apply_production_settings(settings_module):
    """
    Apply all production settings to the Django settings module.
    Call this from your production settings file.
    """
    # Database
    settings_module.DATABASES = PRODUCTION_DATABASES
    
    # Cache and Redis
    settings_module.CACHES = PRODUCTION_REDIS_CONFIG['CACHES']
    settings_module.SESSION_ENGINE = PRODUCTION_REDIS_CONFIG['SESSION_ENGINE']
    settings_module.SESSION_CACHE_ALIAS = PRODUCTION_REDIS_CONFIG['SESSION_CACHE_ALIAS']
    settings_module.SESSION_COOKIE_AGE = PRODUCTION_REDIS_CONFIG['SESSION_COOKIE_AGE']
    settings_module.SESSION_SAVE_EVERY_REQUEST = PRODUCTION_REDIS_CONFIG['SESSION_SAVE_EVERY_REQUEST']
    
    # Channel layers
    settings_module.CHANNEL_LAYERS = PRODUCTION_CHANNEL_LAYERS
    
    # Async settings
    for key, value in PRODUCTION_ASYNC_SETTINGS.items():
        setattr(settings_module, key, value)
    
    # Logging
    settings_module.LOGGING = PRODUCTION_LOGGING
    
    # Security
    for key, value in PRODUCTION_SECURITY.items():
        setattr(settings_module, key, value)
    
    # CORS
    for key, value in PRODUCTION_CORS.items():
        setattr(settings_module, key, value)
    
    print("✅ Production async settings applied successfully!")
    print("🚀 Backend is ready for high-concurrency production deployment!")
