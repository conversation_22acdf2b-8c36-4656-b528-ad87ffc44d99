from django.core.management.base import BaseCommand
from transformer.models import Basestation, TransformerData, Inspection, LvFeeder
from datetime import datetime

class Command(BaseCommand):
    help = 'Delete Basestation records with station_code from CE01-0001 to CE01-0550'

    def handle(self, *args, **kwargs):
        self.stdout.write("Deleting Basestation records from station_code CE01-0001 to CE01-0550")

        # Delete Basestation records in the specified range
        deleted_count, _ = Basestation.objects.filter(
            station_code__gte='CE01-0001',
            station_code__lte='CE01-0550'
        ).delete()

        self.stdout.write(self.style.SUCCESS(
            f"Successfully deleted {deleted_count} Basestation records from CE01-0001 to CE01-0550"
        ))