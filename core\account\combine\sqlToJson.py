import re
import json
import os

def parse_sql_dump(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Extract all region data
    regions = []
    region_pattern = r"\((\d+),\s*'([^']+)',\s*'[^']+',\s*'[^']+'\)"
    region_matches = re.findall(region_pattern, content)
    for match in region_matches:
        rid = int(match[0])
        name = match[1]
        regions.append({"id": rid, "name": name})

    # Extract all substation data
    substations = []
    substation_pattern = r"\((\d+),\s*'([^']+)',\s*(\d+),\s*'[^']+',\s*'[^']+'\)"
    substation_matches = re.findall(substation_pattern, content)
    for match in substation_matches:
        sid = int(match[0])
        name = match[1]
        rid = int(match[2])
        substations.append({"id": sid, "name": name, "region_id": rid})

    # Extract all feeder data
    feeders = []
    feeder_pattern = r"\((\d+),\s*'([^']+)',\s*(\d+),\s*\d+,\s*[^,]*,\s*[^,]*,\s*[^,]*,\s*[^,]*,\s*[^,]*,\s*'[^']+',\s*'[^']+'\)"
    feeder_matches = re.findall(feeder_pattern, content)
    for match in feeder_matches:
        fid = int(match[0])
        name = match[1]
        sid = int(match[2])
        feeders.append({"id": fid, "feeder_name": name, "substation_id": sid})

    # Build the final structure
    result = []
    for region in regions:
        region_entry = {
            "name": region["name"],
            "substations": []
        }

        region_subs = [s for s in substations if s["region_id"] == region["id"]]
        for sub in region_subs:
            sub_entry = {
                "name": sub["name"],
                "feeders": []
            }

            sub_feeders = [f for f in feeders if f["substation_id"] == sub["id"]]
            sub_entry["feeders"] = [{"feeder_name": f["feeder_name"]} for f in sub_feeders]
            region_entry["substations"].append(sub_entry)

        result.append(region_entry)

    return result

if __name__ == "__main__":
    # Get the directory of the current script
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Path to the SQL file
    sql_file_path = os.path.join(current_dir, "boruData.sql")
    
    # Parse the SQL file
    data = parse_sql_dump(sql_file_path)
    
    # Print the JSON data
    print(json.dumps(data, indent=2))
    
    # Save to a Python file
    output_path = os.path.join(current_dir, "new_substation_data.py")
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("substation_data = ")
        f.write(json.dumps(data, indent=2))
        print(f"\nData saved to {output_path}")
