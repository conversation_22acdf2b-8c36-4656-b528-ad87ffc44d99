import os
import pandas as pd
from django.core.management.base import BaseCommand
from transformer.models import Basestation, TransformerData, Inspection, LvFeeder
from faker import Faker
import random



def convert_gps_location(gps_point):
    """
    Converts GPS location from 'POINT(x y)' format to 'x, y' format.
    """
    if not isinstance(gps_point, str) or not gps_point.startswith("POINT("):
        return None  # Return None if the format is invalid
    try:
        # Extract the coordinates from the POINT() string
        coords = gps_point.strip("POINT()").split()
        if len(coords) == 2:
            return f"{coords[1]}, {coords[0]}"  # Latitude first, then longitude
        else:
            return None  # Invalid format
    except Exception:
        return None  # Handle any unexpected errors


class Command(BaseCommand):
    help = 'Import data from CSV into the database and delete existing data'

    def add_arguments(self, parser):
        parser.add_argument('csv_file', type=str, help='Path to the CSV file')

    def handle(self, *args, **kwargs):
        csv_file = kwargs['csv_file']

        if not os.path.exists(csv_file):
            self.stdout.write(self.style.ERROR(f"File '{csv_file}' does not exist."))
            return

        try:
            # Initialize Faker instance
            fake = Faker()

            # Delete all existing data in related models
            self.stdout.write("Deleting existing data...")
            Basestation.objects.all().delete()
            TransformerData.objects.all().delete()
            Inspection.objects.all().delete()
            LvFeeder.objects.all().delete()

            # Read the CSV file into a DataFrame
            df = pd.read_csv(csv_file)

            # Lists to store created objects for linking
            basestations = []
            transformers = []

            # Step 1: Create Basestation and TransformerData instances
            for index, row in df.iterrows():
                # Convert GPS location from 'POINT(x y)' to 'x, y'
                gps_location = convert_gps_location(row.get('gps_location'))

                # Skip rows where GPS location conversion fails
                if not gps_location:
                    self.stdout.write(self.style.WARNING(f"Skipping row {index + 1}: Invalid GPS location format."))
                    continue

                # Create Basestation instance
                basestation_data = {
                    'station_code': row['station_code'],
                    'region': row['region'],
                    'csc': row['csc'],
                    'substation': row['substation'],
                    'feeder': row['feeder'],
                    'station_type': fake.random_element(elements=[choice[0] for choice in Basestation.STATION_TYPES]),
                    'address': row['address'],
                    'gps_location': gps_location,
                }
                basestation = Basestation.objects.create(**basestation_data)
                basestations.append(basestation)

                self.stdout.write(self.style.SUCCESS(f"basestation.station_code {basestation.station_code}."))

                # Create TransformerData instance
                transformer_data = {
                    'basestation': basestation,
                    'trafo_type': fake.random_element(elements=[choice[0] for choice in TransformerData.TRANSFORMER_TYPES]),
                    'capacity': fake.random_element(elements=[choice[0] for choice in TransformerData.CAPACITY_CHOICES]),
                    'dt_number': f"DT_{index + 1}",
                    'primary_voltage': fake.random_element(elements=[choice[0] for choice in TransformerData.PRIMARY_VOLTAGE_CHOICES]),
                    'colling_type': fake.random_element(elements=[choice[0] for choice in TransformerData.COOLING_TYPE_CHOICES]),
                    'serial_number': f"SNO_{index + 1}",
                    'service_type': fake.random_element(elements=[choice[0] for choice in TransformerData.SERVICE_TYPE_CHOICES]),
                    'status': fake.random_element(elements=[choice[0] for choice in TransformerData.STATUS_CHOICES]),
                    'manufacturer': fake.random_element(elements=[choice[0] for choice in TransformerData.MANUFACTURER_CHOICES]),
                    'vector_group': fake.random_element(elements=[choice[0] for choice in TransformerData.VECTOR_GROUP_CHOICES]),
                    'impedance_voltage': fake.random_int(min=4, max=8),
                    'winding_weight': fake.random_int(min=500, max=2000),
                    'oil_weight': fake.random_int(min=100, max=500),
                    'year_of_manufacturing': fake.year(),
                    'date_of_installation': fake.date_between(start_date="-10y", end_date="today"),
                }
                transformer = TransformerData.objects.create(**transformer_data)
                transformers.append(transformer)

                # Generate inspections
                inspections = []
                for _ in range(2):  # Generate 2 inspections per transformer
                    try:
                        inspection_data = {
                            'body_condition': fake.random_element(elements=[choice[0] for choice in Inspection.CONDITION_CHOICES]),
                            'arrester': fake.random_element(elements=[choice[0] for choice in Inspection.STATUS_CHOICES]),
                            'drop_out': fake.random_element(elements=[choice[0] for choice in Inspection.STATUS_CHOICES]),
                            'fuse_link': fake.random_element(elements=[choice[0] for choice in Inspection.STATUS_CHOICES]),
                            'mv_bushing': fake.random_element(elements=[choice[0] for choice in Inspection.STATUS_CHOICES]),
                            'mv_cable_lug': fake.random_element(elements=[choice[0] for choice in Inspection.STATUS_CHOICES]),
                            'lv_bushing': fake.random_element(elements=[choice[0] for choice in Inspection.STATUS_CHOICES]),
                            'lv_cable_lug': fake.random_element(elements=[choice[0] for choice in Inspection.STATUS_CHOICES]),
                            'oil_level': fake.random_element(elements=[choice[0] for choice in Inspection.OIL_LEVEL_CHOICES]),
                            'insulation_level': fake.random_element(elements=[choice[0] for choice in Inspection.INSULATION_LEVEL_CHOICES]),
                            'horn_gap': fake.random_element(elements=[choice[0] for choice in Inspection.HORN_GAP_CHOICES]),
                            'silica_gel': fake.random_element(elements=[choice[0] for choice in Inspection.CONDITION_CHOICES]),
                            'has_linkage': fake.random_element(elements=[choice[0] for choice in Inspection.YES_NO_CHOICES]),
                            'arrester_body_ground': fake.random_element(elements=[choice[0] for choice in Inspection.AVAILABLE_CHOICES]),
                            'neutral_ground': fake.random_element(elements=[choice[0] for choice in Inspection.AVAILABLE_CHOICES]),
                            'status_of_mounting': fake.random_element(elements=[choice[0] for choice in Inspection.CONDITION_CHOICES]),
                            'mounting_condition': fake.random_element(elements=[choice[0] for choice in Inspection.CONDITION_CHOICES]),
                            'N_load_current': fake.random_int(min=0, max=100),
                            'R_S_Voltage': fake.random_int(min=200, max=400),
                            'R_T_Voltage': fake.random_int(min=200, max=400),
                            'T_S_Voltage': fake.random_int(min=200, max=400),
                            'transformer_data': transformer
                        }

                        # Calculate voltage_phase_unbalance and average_voltage
                        RS = inspection_data['R_S_Voltage']
                        RT = inspection_data['R_T_Voltage']
                        ST = inspection_data['T_S_Voltage']

                        if RS and RT and ST:
                            max_diff = max(abs(RS-RT), abs(RS-ST), abs(RT-ST))
                            avg_voltage = (RS + RT + ST) / 3
                            voltage_phase_unbalance = (max_diff / avg_voltage) * 100 if avg_voltage != 0 else 0
                            average_voltage = avg_voltage
                        else:
                            voltage_phase_unbalance = 0
                            average_voltage = 0

                        inspection_data['voltage_phase_unbalance'] = round(voltage_phase_unbalance, 2)
                        inspection_data['average_voltage'] = round(average_voltage, 2)

                        inspection = Inspection.objects.create(**inspection_data)
                        inspections.append(inspection)

                        # Step 3: Generate dummy LV feeder data
                        lv_feeders = []
                        for _ in range(2):  # Generate 2 LV feeders per inspection
                            try:
                                # Generate base LV feeder data first
                                lv_feeder_data = {
                                    'distribution_box_name': fake.word(ext_word_list=["Box 1", "Box 2", "Box 3"]),
                                    'R_load_current': fake.random_int(min=0, max=100),
                                    'S_load_current': fake.random_int(min=0, max=100),
                                    'T_load_current': fake.random_int(min=0, max=100),
                                    'R_fuse_rating': fake.random_int(min=10, max=50),
                                    'S_fuse_rating': fake.random_int(min=10, max=50),
                                    'T_fuse_rating': fake.random_int(min=10, max=50),
                                    'inspection_data': inspection
                                }

                                # Safe calculations
                                try:
                                    # Get load currents
                                    R = float(lv_feeder_data['R_load_current'])
                                    S = float(lv_feeder_data['S_load_current'])
                                    T = float(lv_feeder_data['T_load_current'])
                                    N = float(inspection.N_load_current or 0)

                                    # Get transformer capacity - handle 'null' string case
                                    try:
                                        capacity = float(transformer.capacity) if transformer.capacity not in ('null', None, '') else 0
                                    except (ValueError, TypeError):
                                        capacity = 0

                                    # Calculate Iconst
                                    Iconst = capacity / (3 ** 0.5 * 0.4) if capacity > 0 else 1  # avoid division by zero

                                    # Calculate average current
                                    Iavg = (R + S + T) / 3

                                    # Calculate metrics
                                    transformer_load = (Iavg / Iconst) * 100 if Iconst > 0 else 0
                                    current_phase_unbalance = (max(abs(R-S), abs(R-T), abs(S-T)) / Iavg) * 100 if Iavg > 0 else 0
                                    percentage_of_neutral = (N / Iavg) * 100 if Iavg > 0 else 0

                                    # Update LV feeder data with calculated values
                                    lv_feeder_data.update({
                                        'transformer_load': round(transformer_load, 2),
                                        'current_phase_unbalance': round(current_phase_unbalance, 2),
                                        'percentage_of_neutral': round(percentage_of_neutral, 2)
                                    })

                                except (ValueError, TypeError, ZeroDivisionError) as calc_error:
                                    # If calculations fail, use default values
                                    lv_feeder_data.update({
                                        'transformer_load': 0,
                                        'current_phase_unbalance': 0,
                                        'percentage_of_neutral': 0
                                    })
                                    self.stdout.write(self.style.WARNING(f"Calculation warning: {calc_error}. Using default values."))

                                # Create LV feeder with the data
                                lv_feeder = LvFeeder.objects.create(**lv_feeder_data)
                                lv_feeders.append(lv_feeder)

                            except Exception as e:
                                self.stdout.write(self.style.ERROR(f"Failed to create LV feeder: {e}"))

                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f"Failed to create inspection: {e}"))


                

            # Final success message
            self.stdout.write(self.style.SUCCESS("Data imported successfully."))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"An error occurred: {e}"))
