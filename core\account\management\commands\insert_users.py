# from django.core.management.base import BaseCommand
# from account.models import User
# import pandas as pd

# class Command(BaseCommand):
#     help = 'Import users from Excel (.xlsx) file - create or update'

#     def add_arguments(self, parser):
#         parser.add_argument('file_path', type=str, help='Path to the Excel (.xlsx) file')

#     def handle(self, *args, **kwargs):
#         file_path = kwargs['file_path']

#         try:
#             df = pd.read_excel(file_path)

#             # Normalize column names
#             df.columns = df.columns.str.strip().str.replace(' ', '_').str.lower()

#             users_to_create = []
#             users_to_update = []

#             usernames_in_file = []

#             for _, row in df.iterrows():
#                 username = str(row.get('username')).strip()
#                 full_name = row.get('full_name')
#                 region = row.get('region')
#                 title = row.get('title')

#                 if not username:
#                     self.stdout.write(self.style.WARNING("Skipping row: missing username"))
#                     continue

#                 usernames_in_file.append(username)

#                 email = f"{username}@eeu.com"

#                 user_data = {
#                     'full_name': full_name,
#                     'region': region,
#                     'title': title,
#                     'email': email,
#                     'is_staff': True,
#                     'is_active': True,
#                     'role_id': 1,
#                 }

#                 # Try to find existing user
#                 try:
#                     user = User.objects.get(username=username)
#                     # Update fields
#                     for field, value in user_data.items():
#                         setattr(user, field, value)
#                     users_to_update.append(user)
#                 except User.DoesNotExist:
#                     # Create new user
#                     users_to_create.append(User(username=username, **user_data))

#             # Bulk create
#             if users_to_create:
#                 User.objects.bulk_create(users_to_create)
#                 self.stdout.write(self.style.SUCCESS(f"Created {len(users_to_create)} users."))

#             # Bulk update
#             if users_to_update:
#                 User.objects.bulk_update(
#                     users_to_update,
#                     fields=['full_name', 'region', 'title', 'email', 'role_id', 'is_staff', 'is_active']
#                 )
#                 self.stdout.write(self.style.SUCCESS(f"Updated {len(users_to_update)} users."))

#         except Exception as e:
#             self.stderr.write(self.style.ERROR(f'Error importing users: {e}'))




from django.core.management.base import BaseCommand
from account.models import User
import pandas as pd
from django.contrib.auth.hashers import make_password

class Command(BaseCommand):
    help = 'Import users from Excel (.xlsx) file - create or update with default password'

    def add_arguments(self, parser):
        parser.add_argument('file_path', type=str, help='Path to the Excel (.xlsx) file')

    def handle(self, *args, **kwargs):
        file_path = kwargs['file_path']
        default_password = 'Eeu@1234'

        try:
            df = pd.read_excel(file_path)

            # Normalize column names
            df.columns = df.columns.str.strip().str.replace(' ', '_').str.lower()

            users_to_create = []
            users_to_update = []

            usernames_in_file = []

            for _, row in df.iterrows():
                username = str(row.get('username')).strip()
                full_name = row.get('full_name')
                region = row.get('region')
                title = row.get('title')

                if not username:
                    self.stdout.write(self.style.WARNING("Skipping row: missing username"))
                    continue

                usernames_in_file.append(username)

                email = f"{username}@eeu.com"

                user_data = {
                    'full_name': full_name,
                    'region': region,
                    'title': title,
                    'email': email,
                    'is_staff': True,
                    'is_active': True,
                    'role_id': 1,
                }

                # Try to find existing user
                try:
                    user = User.objects.get(username=username)
                    # Update fields
                    for field, value in user_data.items():
                        setattr(user, field, value)
                    # Set default password for all existing users
                    user.password = make_password(default_password)
                    users_to_update.append(user)
                except User.DoesNotExist:
                    # Create new user with default password
                    user_data['password'] = make_password(default_password)
                    users_to_create.append(User(username=username, **user_data))

            # Bulk create
            if users_to_create:
                User.objects.bulk_create(users_to_create)
                self.stdout.write(self.style.SUCCESS(f"Created {len(users_to_create)} users with default password."))

            # Bulk update
            if users_to_update:
                User.objects.bulk_update(
                    users_to_update,
                    fields=['full_name', 'region', 'title', 'email', 'role_id', 'is_staff', 'is_active', 'password']
                )
                self.stdout.write(self.style.SUCCESS(f"Updated {len(users_to_update)} users with default password."))

        except Exception as e:
            self.stderr.write(self.style.ERROR(f'Error importing users: {e}'))