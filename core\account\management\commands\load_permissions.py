# your_app/management/commands/load_permissions.py

from django.core.management.base import BaseCommand
from account.models import Permission  # Replace 'your_app' with your actual app name

class Command(BaseCommand):
    help = "Load permission data into the database"

    def handle(self, *args, **kwargs):
        # Define the permission types and statuses as constants
        PERMISSION_TYPE_MAP = {
            "PermissionType.CATALOGUE": 0,
            "PermissionType.MENU": 1,
            "PermissionType.BUTTON": 2,
        }

        BASIC_STATUS_MAP = {
            "BasicStatus.DISABLE": 0,
            "BasicStatus.ENABLE": 1,
        }

        data = [
    {
        "id": "****************",
        "parentId": None,
        "label": "sys.menu.dashboard",
        "name": "Dashboard",
        "icon": "ic-analysis",
        "type": "PermissionType.CATALOGUE",
        "route": "dashboard",
        "order": 1,
        "children": [
            {
                "id": "****************",
                "parentId": "****************",
                "label": "sys.menu.workbench",
                "name": "Workbench",
                "type": "PermissionType.MENU",
                "route": "workbench",
                "component": "/dashboard/workbench/index.tsx",
            },
            {
                "id": "9710971640510357",
                "parentId": "****************",
                "label": "sys.menu.analysis",
                "name": "Analysis",
                "type": "PermissionType.MENU",
                "route": "analysis",
                "component": "/dashboard/analysis/index.tsx",
            },
        ],
    },
    {
        "id": "****************",
        "parentId": None,
        "label": "sys.menu.management",
        "name": "Management",
        "icon": "ic-management",
        "type": "PermissionType.CATALOGUE",
        "route": "management",
        "order": 2,
        "children": [
            {
                "id": "****************",
                "parentId": "****************",
                "label": "sys.menu.user.index",
                "name": "User",
                "type": "PermissionType.CATALOGUE",
                "route": "user",
                "children": [
                    {
                        "id": "4754063958766648",
                        "parentId": "****************",
                        "label": "sys.menu.user.profile",
                        "name": "Profile",
                        "type": "PermissionType.MENU",
                        "route": "profile",
                        "component": "/management/user/profile/index.tsx",
                    },
                    {
                        "id": "****************",
                        "parentId": "****************",
                        "label": "sys.menu.user.account",
                        "name": "Account",
                        "type": "PermissionType.MENU",
                        "route": "account",
                        "component": "/management/user/account/index.tsx",
                    },
                ],
            },
            {
                "id": "****************",
                "parentId": "****************",
                "label": "sys.menu.system.index",
                "name": "System",
                "type": "PermissionType.CATALOGUE",
                "route": "system",
                "children": [
                    {
                        "id": "****************",
                        "parentId": "****************",
                        "label": "sys.menu.system.organization",
                        "name": "Organization",
                        "type": "PermissionType.MENU",
                        "route": "organization",
                        "component": "/management/system/organization/index.tsx",
                    },
                    {
                        "id": "****************",
                        "parentId": "****************",
                        "label": "sys.menu.system.permission",
                        "name": "Permission",
                        "type": "PermissionType.MENU",
                        "route": "permission",
                        "component": "/management/system/permission/index.tsx",
                    },
                    {
                        "id": "1689241785490759",
                        "parentId": "****************",
                        "label": "sys.menu.system.role",
                        "name": "Role",
                        "type": "PermissionType.MENU",
                        "route": "role",
                        "component": "/management/system/role/index.tsx",
                    },
                    {
                        "id": "0157880245365433",
                        "parentId": "****************",
                        "label": "sys.menu.system.user",
                        "name": "User",
                        "type": "PermissionType.MENU",
                        "route": "user",
                        "component": "/management/system/user/index.tsx",
                    },
                    {
                        "id": "0157880245365434",
                        "parentId": "****************",
                        "label": "sys.menu.system.user_detail",
                        "name": "User Detail",
                        "type": "PermissionType.MENU",
                        "route": "user/:id",
                        "component": "/management/system/user/detail.tsx",
                        "hide": True,
                    },
                ],
            },
        ],
    },
    {
        "id": "2271615060673773",
        "parentId": None,
        "label": "sys.menu.components",
        "name": "Components",
        "icon": "solar:widget-5-bold-duotone",
        "type": "PermissionType.CATALOGUE",
        "route": "components",
        "order": 3,
        "children": [
            {
                "id": "2478488238255411",
                "parentId": "2271615060673773",
                "label": "sys.menu.icon",
                "name": "Icon",
                "type": "PermissionType.MENU",
                "route": "icon",
                "component": "/components/icon/index.tsx",
            },
            {
                "id": "6755238352318767",
                "parentId": "2271615060673773",
                "label": "sys.menu.animate",
                "name": "Animate",
                "type": "PermissionType.MENU",
                "route": "animate",
                "component": "/components/animate/index.tsx",
            },
            {
                "id": "9992476513546805",
                "parentId": "2271615060673773",
                "label": "sys.menu.scroll",
                "name": "Scroll",
                "type": "PermissionType.MENU",
                "route": "scroll",
                "component": "/components/scroll/index.tsx",
            },
            {
                "id": "1755562695856395",
                "parentId": "2271615060673773",
                "label": "sys.menu.markdown",
                "name": "Markdown",
                "type": "PermissionType.MENU",
                "route": "markdown",
                "component": "/components/markdown/index.tsx",
            },
            {
                "id": "2122547769468069",
                "parentId": "2271615060673773",
                "label": "sys.menu.editor",
                "name": "Editor",
                "type": "PermissionType.MENU",
                "route": "editor",
                "component": "/components/editor/index.tsx",
            },
            {
                "id": "2501920741714350",
                "parentId": "2271615060673773",
                "label": "sys.menu.i18n",
                "name": "Multi Language",
                "type": "PermissionType.MENU",
                "route": "i18n",
                "component": "/components/multi-language/index.tsx",
            },
            {
                "id": "2013577074467956",
                "parentId": "2271615060673773",
                "label": "sys.menu.upload",
                "name": "Upload",
                "type": "PermissionType.MENU",
                "route": "upload",
                "component": "/components/upload/index.tsx",
            },
            {
                "id": "7749726274771764",
                "parentId": "2271615060673773",
                "label": "sys.menu.chart",
                "name": "Chart",
                "type": "PermissionType.MENU",
                "route": "chart",
                "component": "/components/chart/index.tsx",
            },
            {
                "id": "2013577074467957",
                "parentId": "2271615060673773",
                "label": "sys.menu.toast",
                "name": "Toast",
                "type": "PermissionType.MENU",
                "route": "toast",
                "component": "/components/toast/index.tsx",
            },
        ],
    },
    {
        "id": "8132044808088488",
        "parentId": None,
        "label": "sys.menu.functions",
        "name": "Functions",
        "icon": "solar:plain-2-bold-duotone",
        "type": "PermissionType.CATALOGUE",
        "route": "functions",
        "order": 4,
        "children": [
            {
                "id": "3667930780705750",
                "parentId": "8132044808088488",
                "label": "sys.menu.clipboard",
                "name": "Clipboard",
                "type": "PermissionType.MENU",
                "route": "clipboard",
                "component": "/functions/clipboard/index.tsx",
            },
            {
                "id": "3667930780705751",
                "parentId": "8132044808088488",
                "label": "sys.menu.token_expired",
                "name": "Token Expired",
                "type": "PermissionType.MENU",
                "route": "token-expired",
                "component": "/functions/token-expired/index.tsx",
            },
        ],
    },
    {
        "id": "0194818428516575",
        "parentId": None,
        "label": "sys.menu.menulevel.index",
        "name": "Menu Level",
        "icon": "ic-menulevel",
        "type": "PermissionType.CATALOGUE",
        "route": "menu-level",
        "order": 5,
        "children": [
            {
                "id": "0144431332471389",
                "parentId": "0194818428516575",
                "label": "sys.menu.menulevel.1a",
                "name": "Menu Level 1a",
                "type": "PermissionType.MENU",
                "route": "menu-level-1a",
                "component": "/menu-level/menu-level-1a/index.tsx",
            },
            {
                "id": "7572529636800586",
                "parentId": "0194818428516575",
                "label": "sys.menu.menulevel.1b.index",
                "name": "Menu Level 1b",
                "type": "PermissionType.CATALOGUE",
                "route": "menu-level-1b",
                "children": [
                    {
                        "id": "3653745576583237",
                        "parentId": "7572529636800586",
                        "label": "sys.menu.menulevel.1b.2a",
                        "name": "Menu Level 2a",
                        "type": "PermissionType.MENU",
                        "route": "menu-level-2a",
                        "component": "/menu-level/menu-level-1b/menu-level-2a/index.tsx",
                    },
                    {
                        "id": "4873136353891364",
                        "parentId": "7572529636800586",
                        "label": "sys.menu.menulevel.1b.2b.index",
                        "name": "Menu Level 2b",
                        "type": "PermissionType.CATALOGUE",
                        "route": "menu-level-2b",
                        "children": [
                            {
                                "id": "4233029726998055",
                                "parentId": "4873136353891364",
                                "label": "sys.menu.menulevel.1b.2b.3a",
                                "name": "Menu Level 3a",
                                "type": "PermissionType.MENU",
                                "route": "menu-level-3a",
                                "component": "/menu-level/menu-level-1b/menu-level-2b/menu-level-3a/index.tsx",
                            },
                            {
                                "id": "3298034742548454",
                                "parentId": "4873136353891364",
                                "label": "sys.menu.menulevel.1b.2b.3b",
                                "name": "Menu Level 3b",
                                "type": "PermissionType.MENU",
                                "route": "menu-level-3b",
                                "component": "/menu-level/menu-level-1b/menu-level-2b/menu-level-3b/index.tsx",
                            },
                        ],
                    },
                ],
            },
        ],
    },
    {
        "id": "9406067785553476",
        "parentId": None,
        "label": "sys.menu.error.index",
        "name": "Error",
        "icon": "bxs:error-alt",
        "type": "PermissionType.CATALOGUE",
        "route": "error",
        "order": 6,
        "children": [
            {
                "id": "8557056851997154",
                "parentId": "9406067785553476",
                "label": "sys.menu.error.403",
                "name": "403",
                "type": "PermissionType.MENU",
                "route": "403",
                "component": "/sys/error/Page403.tsx",
            },
            {
                "id": "5095669208159005",
                "parentId": "9406067785553476",
                "label": "sys.menu.error.404",
                "name": "404",
                "type": "PermissionType.MENU",
                "route": "404",
                "component": "/sys/error/Page404.tsx",
            },
            {
                "id": "0225992135973772",
                "parentId": "9406067785553476",
                "label": "sys.menu.error.500",
                "name": "500",
                "type": "PermissionType.MENU",
                "route": "500",
                "component": "/sys/error/Page500.tsx",
            },
        ],
    },
    {
        "id": "3981225257359246",
        "parentId": None,
        "label": "sys.menu.calendar",
        "name": "Calendar",
        "icon": "solar:calendar-bold-duotone",
        "type": "PermissionType.MENU",
        "route": "calendar",
        "component": "/sys/others/calendar/index.tsx",
    },
    {
        "id": "3513985683886393",
        "parentId": None,
        "label": "sys.menu.kanban",
        "name": "Kanban",
        "icon": "solar:clipboard-bold-duotone",
        "type": "PermissionType.MENU",
        "route": "kanban",
        "component": "/sys/others/kanban/index.tsx",
    },
    {
        "id": "5455837930804461",
        "parentId": None,
        "label": "sys.menu.disabled",
        "name": "Disabled",
        "icon": "ic_disabled",
        "type": "PermissionType.MENU",
        "route": "disabled",
        "status": "BasicStatus.DISABLE",
        "component": "/sys/others/calendar/index.tsx",
    },
    {
        "id": "7728048658221587",
        "parentId": None,
        "label": "sys.menu.label",
        "name": "Label",
        "icon": "ic_label",
        "type": "PermissionType.MENU",
        "route": "label",
        "newFeature": True,
        "component": "/sys/others/blank.tsx",
    },
    {
        "id": "5733704222120995",
        "parentId": None,
        "label": "sys.menu.frame",
        "name": "Frame",
        "icon": "ic_external",
        "type": "PermissionType.CATALOGUE",
        "route": "frame",
        "children": [
            {
                "id": "9884486809510480",
                "parentId": "5733704222120995",
                "label": "sys.menu.external_link",
                "name": "External Link",
                "type": "PermissionType.MENU",
                "route": "external_link",
                "hideTab": True,
                "component": "/sys/others/iframe/external-link.tsx",
                "frameSrc": "https://ant.design/",
            },
            {
                "id": "9299640886731819",
                "parentId": "5733704222120995",
                "label": "sys.menu.iframe",
                "name": "Iframe",
                "type": "PermissionType.MENU",
                "route": "frame",
                "component": "/sys/others/iframe/index.tsx",
                "frameSrc": "https://ant.design/",
            },
        ],
    },
    {
        "id": "0941594969900756",
        "parentId": None,
        "label": "sys.menu.blank",
        "name": "Blank",
        "icon": "ic_blank",
        "type": "PermissionType.MENU",
        "route": "blank",
        "component": "/sys/others/blank.tsx",
    },
    {
        "id": "35139856831234",
        "parentId": None,
        "label": "sys.menu.transformer",
        "name": "Transformer",
        "icon": "solar:weigher-bold-duotone",
        "type": "PermissionType.MENU",
        "route": "transformer",
        "component": "/transformer/index.tsx",
    },
    {
        "id": "015788024531235",
        "parentId": None,
        "label": "sys.menu.transformer_detail",
        "name": "Transformer Detail",
        "type": "PermissionType.MENU",
        "route": "transformer/:id",
        "component": "/transformer/detail.tsx",
        "hide": True,
    },
    {
        "id": "351398568381236",
        "parentId": None,
        "label": "sys.menu.basestation",
        "name": "Basestation",
        "icon": "solar:point-on-map-bold-duotone",
        "type": "PermissionType.MENU",
        "route": "basestation",
        "component": "/basestation/index.tsx",
    },
    {
        "id": "01578802451237",
        "parentId": None,
        "label": "sys.menu.inspection_detail",
        "name": "Inspection Detail",
        "type": "PermissionType.MENU",
        "route": "transformer/inspection/:id",
        "component": "/transformer/inspection/detail.tsx",
        "hide": True,
    },
    {
        "id": "01578802412348",
        "parentId": None,
        "label": "sys.menu.LvFeeder_detail",
        "name": "LvFeeder Detail",
        "type": "PermissionType.MENU",
        "route": "transformer/inspection/LvFeeder/:id",
        "component": "/transformer/inspection/LvFeeder/detail.tsx",
        "hide": True,
    },
]
        
        self.stdout.write("Deleting all existing permissions...")
        Permission.objects.all().delete()

        def create_permission(permission_data, parent=None):
            """Recursively create permissions."""
            # Remove any existing ID from the data
            permission_data_copy = permission_data.copy()
            permission_data_copy.pop('id', None)
            
            permission = Permission.objects.create(
                parentId=parent,
                label=permission_data["label"],
                name=permission_data["name"],
                icon=permission_data.get("icon", ""),
                type=PERMISSION_TYPE_MAP.get(permission_data["type"], 1),
                route=permission_data.get("route", ""),
                component=permission_data.get("component", ""),
                order=permission_data.get("order"),
                hide=permission_data.get("hide", False),
                status=BASIC_STATUS_MAP.get(permission_data.get("status"), 1),
                new_feature=permission_data.get("new_feature", False),
                frame_src=permission_data.get("frame_src", "")
            )
            self.stdout.write(f"Created permission: {permission.name} (ID: {permission.id})")

            # Recursively create child permissions
            if "children" in permission_data:
                for child_data in permission_data["children"]:
                    create_permission(child_data, parent=permission)

        # Load the data
        for item in data:
            create_permission(item)

        
        self.stdout.write(self.style.SUCCESS("Permissions loaded successfully!"))
