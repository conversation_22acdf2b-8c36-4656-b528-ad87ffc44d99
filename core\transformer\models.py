from django.db import models
from account.models import User, Region, CSCCenter, Substation,Feeder
from django.core.validators import MaxValueValidator, MinValueValidator
from django.utils import timezone
from django.utils.timezone import now
import threading
from django.core.exceptions import ValidationError
from django.db.models import Q

# Create your models here.

thread_local = threading.local()

class Basestation(models.Model):
    STATION_TYPES = [
        ('Single Wooden Pole', 'Single Wooden Pole'),
        ('Single Concrete Pole', 'Single Concrete Pole'),
        ('Single Steel Pole', 'Single Steel Pole'),
        ('Double Wooden Pole', 'Double Wooden Pole'),
        ('Double Concrete Pole', 'Double Concrete Pole'),
        ('Double Steel Pole', 'Double Steel Pole'),
        ('Triple Wooden Pole', 'Triple Wooden Pole'),
        ('Triple Concrete Pole', 'Triple Concrete Pole'),
        ('Triple Steel Pole', 'Triple Steel Pole'),
        ('Quadruple Wooden Pole', 'Quadruple Wooden Pole'),
        ('Quadruple Concrete Pole', 'Quadruple Concrete Pole'),
        ('Quadruple Steel Pole', 'Quadruple Steel Pole'),
        ('Ground Seat Foundation Elevated', 'Ground Seat Foundation Elevated'),
        ('Ground Seat Foundation Ground Level', 'Ground Seat Foundation Ground Level'),
        ('Net Station', 'Net Station'),  
    ]

    station_code = models.CharField(max_length=40, primary_key=True, unique=True)
    region = models.CharField(max_length=125)
    regionId = models.ForeignKey(Region, on_delete=models.SET_NULL, related_name='Basestation_region', blank=True, null=True)
    csc = models.CharField(max_length=125)
    cscId = models.ForeignKey(CSCCenter, on_delete=models.SET_NULL, related_name='Basestation_csc', blank=True, null=True) 
    substation = models.CharField(max_length=125)
    substationId = models.ForeignKey(Substation, on_delete=models.SET_NULL, related_name='Basestation_substation', blank=True, null=True)
    feeder = models.CharField(max_length=125)
    feederId = models.ForeignKey(Feeder, on_delete=models.SET_NULL, related_name='Basestation_feeder', blank=True, null=True)
    station_type = models.CharField(
        max_length=50,
        choices=STATION_TYPES,
        null=True,
        blank=True
    )
    address = models.CharField(max_length=125)
    gps_location = models.CharField(max_length=125)
    accuracy = models.CharField(max_length=125, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='Basestation_created_by', blank=True, null=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='Basestation_updated_by', blank=True, null=True)

    class Meta:
        ordering = ('-created_at',)

    def save(self, *args, **kwargs):
        # Allow created_by and updated_by to be passed as kwargs
        created_by = kwargs.pop('created_by', None)
        updated_by = kwargs.pop('updated_by', None)

        if not self.pk and created_by:  # New instance
            self.created_by = created_by
        if updated_by:  # Always update if provided
            self.updated_by = updated_by
        self.updated_at = now()  # Explicitly set timestamp
        super().save(*args, **kwargs)



class TransformerData(models.Model):
    # Choice Constants
    TRANSFORMER_TYPES = [
        ('Conservator', 'Conservator'),
        ('Hermatical', 'Hermatical'),
        ('Compact', 'Compact'),
    ]

    CAPACITY_CHOICES = [
        ('10', '10 kVA'),
        ('25', '25 kVA'),
        ('50', '50 kVA'),
        ('100', '100 kVA'),
        ('200', '200 kVA'),
        ('315', '315 kVA'),
        ('400', '400 kVA'),
        ('500', '500 kVA'),
        ('630', '630 kVA'),
        ('800', '800 kVA'),
        ('1250', '1250 kVA'),
        ('2500', '2500 kVA'),
        ('null', 'Other'),
    ]

    PRIMARY_VOLTAGE_CHOICES = [
        ('15', '15 kVA'),
        ('19', '19 kVA'),
        ('33', '33 kVA'),
        ('null', 'Other'),
    ]

    COOLING_TYPE_CHOICES = [
        ('ONAN', 'ONAN'),
        ('Dry Type', 'Dry Type'),
    ]

    VECTOR_GROUP_CHOICES = [
        ('DY1', 'DY1'),
        ('DY5', 'DY5'),
        ('DY11', 'DY11'),
        ('Other', 'Other'),
    ]

    SERVICE_TYPE_CHOICES = [
        ('Dedicated', 'Dedicated'),
        ('Public', 'Public'),
    ]

    STATUS_CHOICES = [
        ('New', 'New'),
        ('Maintained', 'Maintained'),
        ('Damaged', 'Damaged')
    ]

    MANUFACTURER_CHOICES = [
        ('ABB Tanzania', 'ABB Tanzania'),
        ('Apex', 'Apex'),
        ('China Natinal Electric wire and cable Imp/Exp corporations', 
         'China Natinal Electric wire and cable Imp/Exp corporations'),
        ('Iran Transformer', 'Iran Transformer'),
        ('Kobera', 'Kobera'),
        ('Koncar', 'Koncar'),
        ("Mar son's", "Mar son's"),
        ('METEC', 'METEC'),
        ('Minel Transformer', 'Minel Transformer'),
        ('Pauwels', 'Pauwels'),
        ('Stromberg', 'Stromberg'),
        ('Vijai Electrical Ltd Hyderabad', 'Vijai Electrical Ltd Hyderabad'),
        ('Zennaro', 'Zennaro'),
        ('Other', 'Other'),
    ]

    basestation = models.OneToOneField(
    Basestation,
    on_delete=models.SET_NULL,
    related_name='transformer_data_basestation',
    to_field='station_code',
    db_column='basestation_station_code',
    blank=True,
    null=True
    )
   # transformerLocation = models.CharField(max_length=125)
    # transformerStatus = models.CharField(max_length=125)
    trafo_type = models.CharField(
        max_length=50,
        choices=TRANSFORMER_TYPES,
        verbose_name="Transformer Type",
    )
    
    capacity = models.CharField(
        max_length=10,
        choices=CAPACITY_CHOICES,
        verbose_name="Capacity"
    )

    capacity = models.CharField(
        max_length=10,
        verbose_name="Capacity"
    )

#     capacity = models.IntegerField(
#     verbose_name="Capacity"
# )
    
    dt_number = models.CharField(
        max_length=50,
        verbose_name="DT Number",
        blank=True,
        null=True
    )
    
    primary_voltage = models.CharField(
        max_length=10,
        choices=PRIMARY_VOLTAGE_CHOICES,
        verbose_name="Primary Voltage"
    )
    
    colling_type = models.CharField(
        max_length=50,
        choices=COOLING_TYPE_CHOICES,
        verbose_name="Cooling Type"
    )
    
    serial_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name="Serial Number"
    )
    
    service_type = models.CharField(max_length=50, choices=SERVICE_TYPE_CHOICES, null=True, verbose_name="Service Type")
    status = models.CharField(max_length=50, choices=STATUS_CHOICES, null=True, verbose_name="Status")
    
    manufacturer = models.CharField(
        max_length=100,
        choices=MANUFACTURER_CHOICES,
        verbose_name="Manufacturer",
        # blank=True,
        # null=True
    )

    manufacturer = models.CharField(
        max_length=100,
        verbose_name="Manufacturer",
    )
    
    vector_group = models.CharField(
        max_length=10,
        choices=VECTOR_GROUP_CHOICES,
        verbose_name="Vector Group",
    )
    
    impedance_voltage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name="Impedance Voltage (%)"
    )
    
    winding_weight = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name="Winding Weight (kg)"
    )
    
    oil_weight = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name="Oil Weight (kg)"
    )
    
    year_of_manufacturing = models.IntegerField(
        verbose_name="Year of Manufacturing",
        blank=True,
        null=True
    )
    date_of_installation = models.DateField(
        verbose_name="Date of Installation",
        blank=True,
        null=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='TransformerData_created_by', blank=True, null=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='TransformerData_updated_by', blank=True, null=True)
    # basestation_data = models.ForeignKey('Basestation', on_delete=models.SET_NULL,null=True,blank=True, 
    #                                      related_name='transformer_data_basestation_data')
    
    class Meta:
        ordering = ('-created_at',)

    def clean(self):
        if self.serial_number:
            # Convert to lowercase for comparison
            serial_lower = self.serial_number.lower()
            # Check for existing serial numbers, excluding self if updating
            existing = TransformerData.objects.filter(
                serial_number__iexact=serial_lower
            ).exclude(pk=self.pk).exists()
            
            if existing:
                raise ValidationError({
                    'serial_number': 'A transformer with this serial number already exists.'
                })

    def save(self, *args, **kwargs):
        self.full_clean()  # Run validation before saving
        
        # Convert serial number to lowercase before saving
        if self.serial_number:
            self.serial_number = self.serial_number.lower()
            
        # Handle created_by and updated_by
        created_by = kwargs.pop('created_by', None)
        updated_by = kwargs.pop('updated_by', None)

        if not self.pk and created_by:  # New instance
            self.created_by = created_by
        if updated_by:  # Always update if provided
            self.updated_by = updated_by
            
        self.updated_at = now()  # Explicitly set timestamp
        super().save(*args, **kwargs)



class Inspection(models.Model):
    # Choice Constants
    CONDITION_CHOICES = [
        ('Good', 'Good'),
        ('Fair', 'Fair'),
        ('Poor', 'Poor'),
    ]

    STATUS_CHOICES = [
        ('Ok', 'Ok'),
        ('one missed', 'One Missed'),
        ('two missed', 'Two Missed'),
        ('all missed', 'All Missed'),
    ]

    OIL_LEVEL_CHOICES = [
        ('Full', 'Full'),
        ('0.75', '0.75'),
        ('0.5', '0.5'),
        ('0.25', '0.25'),
    ]

    INSULATION_LEVEL_CHOICES = [
        ('Acceptable', 'Acceptable'),
        ('Not Acceptable', 'Not Acceptable'),
    ]

    HORN_GAP_CHOICES = [
        ('Good', 'Good'),
        ('Poor', 'Poor'),
    ]

    YES_NO_CHOICES = [
        ('Yes', 'Yes'),
        ('No', 'No'),
    ]

    AVAILABLE_CHOICES = [
        ('Available', 'Available'),
        ('Not Available', 'Not Available'),
    ]

    # Fields
    # owner = models.CharField(max_length=255)
    body_condition = models.CharField(max_length=50, choices=CONDITION_CHOICES, null=True)
    arrester = models.CharField(max_length=50, choices=STATUS_CHOICES, null=True)
    drop_out = models.CharField(max_length=50, choices=STATUS_CHOICES, null=True)
    fuse_link = models.CharField(max_length=50, choices=STATUS_CHOICES, null=True)
    mv_bushing = models.CharField(max_length=50, choices=STATUS_CHOICES, null=True)
    mv_cable_lug = models.CharField(max_length=50, choices=STATUS_CHOICES, null=True)
    lv_bushing = models.CharField(max_length=50, choices=STATUS_CHOICES, null=True)
    lv_cable_lug = models.CharField(max_length=50, choices=STATUS_CHOICES, null=True)
    oil_level = models.CharField(max_length=50, choices=OIL_LEVEL_CHOICES, null=True)
    insulation_level = models.CharField(max_length=50, choices=INSULATION_LEVEL_CHOICES, null=True)
    horn_gap = models.CharField(max_length=50, choices=HORN_GAP_CHOICES, null=True)
    silica_gel = models.CharField(max_length=50, choices=CONDITION_CHOICES, null=True)
    has_linkage = models.CharField(max_length=50, choices=YES_NO_CHOICES, null=True)
    arrester_body_ground = models.CharField(max_length=50, choices=AVAILABLE_CHOICES, null=True)
    neutral_ground = models.CharField(max_length=50, choices=AVAILABLE_CHOICES, null=True)
    status_of_mounting = models.CharField(max_length=50, choices=CONDITION_CHOICES, null=True)
    mounting_condition = models.CharField(max_length=50, choices=CONDITION_CHOICES, null=True)
    
    # Numeric fields
    N_load_current = models.DecimalField(max_digits=10, decimal_places=2)
    R_S_Voltage = models.DecimalField(max_digits=10, decimal_places=2)
    R_T_Voltage = models.DecimalField(max_digits=10, decimal_places=2)
    T_S_Voltage = models.DecimalField(max_digits=10, decimal_places=2)
    gps_location = models.CharField(max_length=125, blank=True, null=True)
    accuracy = models.CharField(max_length=125, blank=True, null=True)

    voltage_phase_unbalance = models.DecimalField(max_digits=10, decimal_places=2, blank=True,null=True)
    average_voltage = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)

    # Add total transformer load field
    total_transformer_load = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        null=True, 
        blank=True,
        verbose_name='Total Transformer Load'
    )

    # Relationships and metadata
    transformer_data = models.ForeignKey('TransformerData', on_delete=models.CASCADE, related_name='inspections')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='inspections_created_by', blank=True, null=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='inspections_updated_by', blank=True, null=True)    
    transformer_data = models.ForeignKey('TransformerData', on_delete=models.CASCADE,
                                          related_name='inspections_transformer_data')
    
    class Meta:
        ordering = ('-created_at',)

    def save(self, *args, **kwargs):
        # Allow created_by and updated_by to be passed as kwargs
        created_by = kwargs.pop('created_by', None)
        updated_by = kwargs.pop('updated_by', None)

        if not self.pk and created_by:  # New instance
            self.created_by = created_by
        if updated_by:  # Always update if provided
            self.updated_by = updated_by
        self.updated_at = now()  # Explicitly set timestamp
        super().save(*args, **kwargs)


    

class LvFeeder(models.Model):

    FUSE_RATING_CHOICES = [
        ('63', '63 A'),
        ('80', '80  A'),
        ('100', '100 A'),
        ('150', '150 A'),
        ('160', '160 A'),
        ('200', '200 A'),
        ('250', '250 A'),
        ('300', '300 A'),
        ('315', '315 A'),
        ('350', '350 A'),
        ('400', '400 A'),
        ('500', '500 A'),
        ('600', '600 A'),
        ('700', '700 A'),
        ('800', '800 A'),
        ('Direct', 'Direct'),
        ('other', 'Other'),
    ]

    distribution_box_name = models.CharField(
        max_length=125,
        verbose_name="Distribution Box Name"
    )
    
    R_load_current = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        verbose_name="R Load Current (A)"
    )
    
    S_load_current = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        verbose_name="S Load Current (A)"
    )
    
    T_load_current = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        verbose_name="T Load Current (A)"
    )
    
    R_fuse_rating = models.CharField(
        max_length=10,
        choices=FUSE_RATING_CHOICES,
        verbose_name="R Fuse Rating"
    )
    
    S_fuse_rating = models.CharField(
        max_length=10,
        choices=FUSE_RATING_CHOICES,
        verbose_name="S Fuse Rating"
    )
    
    T_fuse_rating = models.CharField(
        max_length=10,
        choices=FUSE_RATING_CHOICES,
        verbose_name="T Fuse Rating"
    )

    transformer_load = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        verbose_name="Transformer Load",
        blank=True,
        null=True
    )
    current_phase_unbalance = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        verbose_name="Current Phase Unbalance",
        blank=True,
        null=True
    )
    percentage_of_neutral = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        verbose_name="Percentage of Neutral Load",
        blank=True,
        null=True
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='LvFeeder_created_by', blank=True, null=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='LvFeeder_updated_by', blank=True, null=True)    
    inspection_data = models.ForeignKey('Inspection', on_delete=models.CASCADE,
                                          related_name='LvFeeder_inspection_data')
    
    class Meta:
        ordering = ('-created_at',)

    def save(self, *args, **kwargs):
        # Allow created_by and updated_by to be passed as kwargs
        created_by = kwargs.pop('created_by', None)
        updated_by = kwargs.pop('updated_by', None)

        if not self.pk and created_by:  # New instance
            self.created_by = created_by
        if updated_by:  # Always update if provided
            self.updated_by = updated_by
        self.updated_at = now()  # Explicitly set timestamp
        super().save(*args, **kwargs)

