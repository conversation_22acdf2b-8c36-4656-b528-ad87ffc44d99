from django.db import models
from account.models import User
from django.utils.timezone import now
from transformer.models import Basestation, TransformerData, Inspection, LvFeeder

class ActivityLog(models.Model):
    ACTION_CHOICES = [
        ('CREATE', 'Create'),
        ('UPDATE', 'Update'),
        ('DELETE', 'Delete'),
    ]

    MODEL_CHOICES = [
        ('Basestation', 'Basestation'),
        ('TransformerData', 'Transformer Data'),
        ('Inspection', 'Inspection'),
        ('LvFeeder', 'LV Feeder'),
    ]

    action = models.CharField(max_length=10, choices=ACTION_CHOICES)
    model_name = models.CharField(max_length=20, choices=MODEL_CHOICES)
    record_id = models.CharField(max_length=1000)  # Store the primary key of the affected record
    changes = models.JSONField()  # Store the changes in JSON format
    timestamp = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'Activity Log'
        verbose_name_plural = 'Activity Logs'

class ErrorLog(models.Model):
    ERROR_LEVELS = [
        ('INFO', 'Information'),
        ('WARNING', 'Warning'),
        ('ERROR', 'Error'),
        ('CRITICAL', 'Critical'),
    ]

    level = models.CharField(max_length=10, choices=ERROR_LEVELS)
    message = models.TextField()
    traceback = models.TextField(null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'Error Log'
        verbose_name_plural = 'Error Logs'

class DataChangeLog(models.Model):
    # Foreign keys to all possible models
    basestation = models.ForeignKey(Basestation, on_delete=models.SET_NULL, null=True, blank=True)
    transformer_data = models.ForeignKey(TransformerData, on_delete=models.SET_NULL, null=True, blank=True)
    inspection = models.ForeignKey(Inspection, on_delete=models.SET_NULL, null=True, blank=True)
    lv_feeder = models.ForeignKey(LvFeeder, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Add new fields
    model_name = models.CharField(max_length=20, choices=[
        ('Basestation', 'Basestation'),
        ('TransformerData', 'Transformer Data'),
        ('Inspection', 'Inspection'),
        ('LvFeeder', 'LV Feeder'),
    ])
    record_id = models.CharField(max_length=50)  # To store the specific record identifier
    
    field_name = models.CharField(max_length=100)
    old_value = models.TextField(null=True, blank=True)
    new_value = models.TextField(null=True, blank=True)
    change_type = models.CharField(max_length=20)  # e.g., "modification", "creation", "deletion"
    reason = models.TextField(null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    changed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'Data Change Log'
        verbose_name_plural = 'Data Change Logs'

    def save(self, *args, **kwargs):
        if not self.timestamp:
            self.timestamp = now()
        super().save(*args, **kwargs)
