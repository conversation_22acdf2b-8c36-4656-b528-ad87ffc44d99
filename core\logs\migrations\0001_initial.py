# Generated by Django 5.1.6 on 2025-07-16 13:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('transformer', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('CREATE', 'Create'), ('UPDATE', 'Update'), ('DELETE', 'Delete')], max_length=10)),
                ('model_name', models.CharField(choices=[('Basestation', 'Basestation'), ('TransformerData', 'Transformer Data'), ('Inspection', 'Inspection'), ('LvFeeder', 'LV Feeder')], max_length=20)),
                ('record_id', models.Char<PERSON>ield(max_length=50)),
                ('changes', models.JSONField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Activity Log',
                'verbose_name_plural': 'Activity Logs',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='DataChangeLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_name', models.CharField(choices=[('Basestation', 'Basestation'), ('TransformerData', 'Transformer Data'), ('Inspection', 'Inspection'), ('LvFeeder', 'LV Feeder')], max_length=20)),
                ('record_id', models.CharField(max_length=50)),
                ('field_name', models.CharField(max_length=100)),
                ('old_value', models.TextField(blank=True, null=True)),
                ('new_value', models.TextField(blank=True, null=True)),
                ('change_type', models.CharField(max_length=20)),
                ('reason', models.TextField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('basestation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='transformer.basestation')),
                ('changed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('inspection', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='transformer.inspection')),
                ('lv_feeder', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='transformer.lvfeeder')),
                ('transformer_data', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='transformer.transformerdata')),
            ],
            options={
                'verbose_name': 'Data Change Log',
                'verbose_name_plural': 'Data Change Logs',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='ErrorLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.CharField(choices=[('INFO', 'Information'), ('WARNING', 'Warning'), ('ERROR', 'Error'), ('CRITICAL', 'Critical')], max_length=10)),
                ('message', models.TextField()),
                ('traceback', models.TextField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Error Log',
                'verbose_name_plural': 'Error Logs',
                'ordering': ['-timestamp'],
            },
        ),
    ]
