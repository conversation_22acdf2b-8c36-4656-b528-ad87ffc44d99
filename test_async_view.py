#!/usr/bin/env python
"""
Test script for AsyncBasestationsFilteredAPIView
Tests concurrent requests to verify the async implementation handles high load without connection issues.
"""

import asyncio
import aiohttp
import time
import json
from concurrent.futures import ThreadPoolExecutor
import threading

# Configuration
BASE_URL = "http://localhost:8000/api/transformer/basestationsFilteredAsync/"
NUM_CONCURRENT_REQUESTS = 20
TEST_PARAMS = [
    {"searchType": "BaseStation", "pageSize": "50"},
    {"searchType": "Transformer", "pageSize": "100"},
    {"searchType": "BaseStation", "region": "North", "pageSize": "25"},
    {"searchType": "Transformer", "without_base_station": "true", "pageSize": "30"},
]

async def make_request(session, url, params, request_id):
    """Make a single async request"""
    try:
        start_time = time.time()
        async with session.get(url, params=params) as response:
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status == 200:
                data = await response.json()
                return {
                    'request_id': request_id,
                    'status': 'success',
                    'response_time': response_time,
                    'status_code': response.status,
                    'data_count': len(data.get('results', [])) if isinstance(data, dict) else len(data),
                    'async_processed': data.get('async_processed', False) if isinstance(data, dict) else False
                }
            else:
                error_text = await response.text()
                return {
                    'request_id': request_id,
                    'status': 'error',
                    'response_time': response_time,
                    'status_code': response.status,
                    'error': error_text[:200]  # Truncate long errors
                }
    except Exception as e:
        return {
            'request_id': request_id,
            'status': 'exception',
            'response_time': 0,
            'error': str(e)
        }

async def run_concurrent_test():
    """Run concurrent requests to test async performance"""
    print(f"Starting concurrent test with {NUM_CONCURRENT_REQUESTS} requests...")
    print(f"Target URL: {BASE_URL}")
    
    # Create aiohttp session with connection pooling
    connector = aiohttp.TCPConnector(
        limit=50,  # Total connection pool size
        limit_per_host=30,  # Max connections per host
        ttl_dns_cache=300,  # DNS cache TTL
        use_dns_cache=True,
    )
    
    timeout = aiohttp.ClientTimeout(total=60)  # 60 second timeout
    
    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
        # Create tasks for concurrent requests
        tasks = []
        for i in range(NUM_CONCURRENT_REQUESTS):
            # Cycle through different test parameters
            params = TEST_PARAMS[i % len(TEST_PARAMS)]
            task = make_request(session, BASE_URL, params, i + 1)
            tasks.append(task)
        
        # Run all requests concurrently
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Analyze results
        total_time = end_time - start_time
        successful_requests = [r for r in results if isinstance(r, dict) and r.get('status') == 'success']
        failed_requests = [r for r in results if isinstance(r, dict) and r.get('status') != 'success']
        exceptions = [r for r in results if not isinstance(r, dict)]
        
        print(f"\n=== Test Results ===")
        print(f"Total time: {total_time:.2f} seconds")
        print(f"Successful requests: {len(successful_requests)}/{NUM_CONCURRENT_REQUESTS}")
        print(f"Failed requests: {len(failed_requests)}")
        print(f"Exceptions: {len(exceptions)}")
        
        if successful_requests:
            avg_response_time = sum(r['response_time'] for r in successful_requests) / len(successful_requests)
            max_response_time = max(r['response_time'] for r in successful_requests)
            min_response_time = min(r['response_time'] for r in successful_requests)
            
            print(f"\n=== Response Times ===")
            print(f"Average: {avg_response_time:.2f}s")
            print(f"Max: {max_response_time:.2f}s")
            print(f"Min: {min_response_time:.2f}s")
            
            # Check if async processing was used
            async_processed_count = sum(1 for r in successful_requests if r.get('async_processed'))
            print(f"Async processed requests: {async_processed_count}/{len(successful_requests)}")
        
        if failed_requests:
            print(f"\n=== Failed Requests ===")
            for req in failed_requests[:5]:  # Show first 5 failures
                print(f"Request {req['request_id']}: Status {req.get('status_code', 'N/A')} - {req.get('error', 'Unknown error')[:100]}")
        
        if exceptions:
            print(f"\n=== Exceptions ===")
            for i, exc in enumerate(exceptions[:3]):  # Show first 3 exceptions
                print(f"Exception {i+1}: {str(exc)[:100]}")
        
        return {
            'total_requests': NUM_CONCURRENT_REQUESTS,
            'successful': len(successful_requests),
            'failed': len(failed_requests),
            'exceptions': len(exceptions),
            'total_time': total_time,
            'avg_response_time': avg_response_time if successful_requests else 0
        }

def run_sync_test():
    """Run the async test"""
    return asyncio.run(run_concurrent_test())

if __name__ == "__main__":
    print("Testing AsyncBasestationsFilteredAPIView under concurrent load...")
    print("Make sure your Django server is running on localhost:8000")
    print("Press Ctrl+C to cancel\n")
    
    try:
        results = run_sync_test()
        
        print(f"\n=== Final Summary ===")
        success_rate = (results['successful'] / results['total_requests']) * 100
        print(f"Success rate: {success_rate:.1f}%")
        
        if success_rate >= 95:
            print("✅ PASS: Async implementation handles concurrent requests well!")
        elif success_rate >= 80:
            print("⚠️  WARNING: Some requests failed, but mostly working")
        else:
            print("❌ FAIL: Too many requests failed, needs investigation")
            
    except KeyboardInterrupt:
        print("\nTest cancelled by user")
    except Exception as e:
        print(f"\nTest failed with exception: {e}")
