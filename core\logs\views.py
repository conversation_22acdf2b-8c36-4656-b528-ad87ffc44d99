from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.decorators import action
from .models import Activity<PERSON>og, <PERSON>rrorLog, DataChangeLog
from rest_framework import serializers, status
from rest_framework.pagination import PageNumberPagination
from django.db.models import Q
import datetime  # Use <PERSON>'s datetime instead of dayjs since this is backend code

class CustomPageNumberPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'pageSize'
    max_page_size = 100
    page_query_param = 'page'

class LogSerializer(serializers.ModelSerializer):
    user = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = ActivityLog
        fields = ['id', 'user', 'action', 'model_name', 'record_id', 'changes', 'timestamp', 'ip_address']

class ErrorLogSerializer(serializers.ModelSerializer):
    user = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = ErrorLog
        fields = ['id', 'level', 'message', 'traceback', 'timestamp', 'user', 'ip_address']

class DataChangeLogSerializer(serializers.ModelSerializer):
    changed_by = serializers.CharField(source='changed_by.username', read_only=True)
    record_id = serializers.SerializerMethodField()
    model_name = serializers.SerializerMethodField()
    
    def get_model_name(self, obj):
        if obj.basestation_id:
            return 'Basestation'
        elif obj.transformer_data_id:
            return 'Transformer Data'
        elif obj.inspection_id:
            return 'Inspection'
        elif obj.lv_feeder_id:
            return 'LV Feeder'
        return None

    def get_record_id(self, obj):
        if obj.basestation_id:
            return f"BS-{obj.basestation.station_code}"
        elif obj.transformer_data_id:
            return f"TR-{obj.transformer_data.id}"  # Using id instead of dt_number
        elif obj.inspection_id:
            return f"IN-{obj.inspection_id}"
        elif obj.lv_feeder_id:
            return f"LV-{obj.lv_feeder_id}"
        return None

    class Meta:
        model = DataChangeLog
        fields = ['id', 'model_name', 'record_id', 'field_name', 'old_value', 
                 'new_value', 'change_type', 'reason', 'timestamp', 'changed_by', 'ip_address']

class LogViewSet(viewsets.ViewSet):
    pagination_class = CustomPageNumberPagination

    def get_paginator(self):
        """
        Create a paginator instance.
        """
        if not hasattr(self, '_paginator'):
            self._paginator = self.pagination_class()
        return self._paginator

    def apply_filters(self, queryset, request):
        filters = Q()
        
        # Date range filters
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        if start_date and end_date:
            filters &= Q(timestamp__range=[start_date, end_date])

        # Common filters
        user = request.query_params.get('user')
        record_id = request.query_params.get('record_id')
        
        if user:
            filters &= Q(user__icontains=user)
        if record_id:
            filters &= Q(record_id__iexact=record_id)

        return queryset.filter(filters)

    @action(detail=False, methods=['get'])
    def activity(self, request):
        try:
            queryset = ActivityLog.objects.all().order_by('-timestamp')
            
            # Apply common filters
            queryset = self.apply_filters(queryset, request)
            
            # Activity specific filters
            action = request.query_params.get('action')
            model_name = request.query_params.get('model_name')
            
            if action:
                queryset = queryset.filter(action=action)
            if model_name:
                queryset = queryset.filter(model_name__icontains=model_name)

            paginator = self.get_paginator()
            page = paginator.paginate_queryset(queryset, request, view=self)
            serializer = LogSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
        except Exception as e:
            return Response({"error": str(e)}, status=500)

    @action(detail=False, methods=['delete'])
    def activity_clear(self, request):
        try:
            ActivityLog.objects.all().delete()
            return Response({"message": "All activity logs cleared successfully"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def errors(self, request):
        try:
            queryset = ErrorLog.objects.all().order_by('-timestamp')
            
            # Apply common filters
            queryset = self.apply_filters(queryset, request)
            
            # Error specific filters
            level = request.query_params.get('level')
            message = request.query_params.get('message')
            
            if level:
                queryset = queryset.filter(level=level)
            if message:
                queryset = queryset.filter(message__icontains=message)

            paginator = self.get_paginator()
            page = paginator.paginate_queryset(queryset, request, view=self)
            serializer = ErrorLogSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
        except Exception as e:
            return Response({"error": str(e)}, status=500)

    @action(detail=False, methods=['delete'])
    def errors_clear(self, request):
        try:
            ErrorLog.objects.all().delete()
            return Response({"message": "All error logs cleared successfully"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def changes(self, request):
        try:
            queryset = DataChangeLog.objects.all().order_by('-timestamp')
            
            # Apply common filters
            queryset = self.apply_filters(queryset, request)
            
            # Change specific filters
            changed_by = request.query_params.get('changed_by')
            model_name = request.query_params.get('model_name')
            field_name = request.query_params.get('field_name')
            
            if changed_by:
                queryset = queryset.filter(changed_by__icontains=changed_by)
            if model_name:
                queryset = queryset.filter(model_name__icontains=model_name)
            if field_name:
                queryset = queryset.filter(field_name__icontains=field_name)

            paginator = self.get_paginator()
            page = paginator.paginate_queryset(queryset, request, view=self)
            serializer = DataChangeLogSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
        except Exception as e:
            return Response({"error": str(e)}, status=500)

    @action(detail=False, methods=['delete'])
    def changes_clear(self, request):
        try:
            DataChangeLog.objects.all().delete()
            return Response({"message": "All change logs cleared successfully"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class DataChangeLogViewSet(viewsets.ModelViewSet):
    queryset = DataChangeLog.objects.all()
    serializer_class = DataChangeLogSerializer
    pagination_class = CustomPageNumberPagination

    def get_paginator(self):
        """
        Create a paginator instance.
        """
        if not hasattr(self, '_paginator'):
            self._paginator = self.pagination_class()
        return self._paginator

    def apply_filters(self, queryset, request):
        filters = Q()
        
        # Date range filters
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        if start_date and end_date:
            filters &= Q(timestamp__range=[start_date, end_date])

        # Common filters
        user = request.query_params.get('user')
        record_id = request.query_params.get('record_id')
        
        if user:
            filters &= Q(user__icontains=user)
        if record_id:
            filters &= Q(record_id__iexact=record_id)

        return queryset.filter(filters)

    @action(detail=True, methods=['get'], url_path='transformer/(?P<transformer_id>[^/.]+)')
    def transformer_changes(self, request, transformer_id=None):
        try:
            queryset = DataChangeLog.objects.filter(
                Q(model_name='TransformerData') & 
                Q(record_id__icontains=f"TR-{transformer_id}")
            ).order_by('-timestamp')

            serializer = DataChangeLogSerializer(queryset, many=True)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
# Create your views here.
